app:
  datasource:
    host: *************:31656
    username: root
    password: Btz<PERSON>@2022
    name: bot_collection_data
  redis:
    host: *************
    database: 1
    port: 7011
    password: Btzn@2022
  rabbitmq:
    host: **************
    port: 5672
    username: admin
    password: <PERSON><PERSON><PERSON>@2021
  elasticsearch:
    host: es-cluster-002.botsmart.cn
    port: 29200
    username: elastic
    password: 02_Qm90c21hcnQ$
  minio:
    port: 9000
    endPoint: http://minio.miyun.botsmart.cn #Minio服务所在地址
    bucketName: botsmart #存储桶名称
    accessKey: YuN2vUABOkg6uMl0 #访问的key
    secretKey: GUMqhs0lHBwHrXHt84cxyj2kME6HeYyX
    collection-result-img-folder: col_result_img/
custom:
  xh-wx-official-accounts-add-task-url: http://127.0.0.1:8200/test/addTask
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl