<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bot.data.collection.server.mapper.DataAuditHistoryMapper">

    <select id="selectAwaitDisposeCountByUserSid" resultType="java.lang.Long">
        SELECT COUNT(1) FROM data_audit_history WHERE deleted = 0 AND is_newest = 0 AND (audit_status = 1 OR audit_status = 3)
                                                  AND task_sid IN (
                SELECT DISTINCT sid FROM `collection_task` WHERE pi_sid IN(
                    SELECT project_info_sid FROM project_info_auditor WHERE type = 0 AND user_sid = #{userSid} AND deleted = 0
                    UNION
                    SELECT project_info_sid FROM project_info_auditor WHERE type = 0 AND user_group_sid IN(
                        SELECT user_group_sid FROM system_user_user_group WHERE user_sid = #{userSid} AND deleted = 0
                    ) AND deleted = 0
                ) AND deleted = 0
            );
    </select>

    <!--<select id="selectTaskAwaitDisposeCountByUserSid" resultType="java.lang.Long">
        SELECT COUNT(1) FROM data_audit_history WHERE deleted = 0 AND is_newest = 0 AND (audit_status = 1 OR audit_status = 3)
             AND task_sid = #{taskSid} AND task_sid IN (
                SELECT DISTINCT sid FROM `collection_task` WHERE pi_sid IN(
                    SELECT project_info_sid FROM project_info_auditor WHERE type = 0 AND user_sid = #{userSid} AND deleted = 0
                    UNION
                    SELECT project_info_sid FROM project_info_auditor WHERE type = 0 AND user_group_sid IN(
                        SELECT user_group_sid FROM system_user_user_group WHERE user_sid = #{userSid} AND deleted = 0
                    ) AND deleted = 0
                ) AND deleted = 0
            );
    </select>-->
    <select id="selectNotFirstAuditStatusDataCount" resultType="java.lang.Long">
        SELECT COUNT(1) FROM data_audit_history WHERE deleted = 0 AND is_newest = 0 AND audit_status != 1 AND audit_status != 3
                                                  AND data_id = #{dataId};
    </select>

    <select id="selectTaskAwaitDisposeCountByUserSid" resultType="java.lang.Long">
        SELECT COUNT(1) FROM data_audit_history WHERE deleted = 0 AND is_newest = 0 AND (audit_status = 1 OR audit_status = 3)
        AND task_sid = #{taskSid};
    </select>

    <select id="selectAlreadyDisposeCountByUserSid" resultType="java.lang.Long">
        SELECT count(distinct a.data_id) FROM data_audit_history a WHERE a.deleted = 0
             AND a.audit_status = 2 AND a.creator = #{userSid}
             AND NOT EXISTS (SELECT b.sid  FROM data_audit_history b WHERE b.deleted = 0 AND b.data_id=a.data_id AND b.audit_status = 3 AND b.create_time>a.create_time );
    </select>

    <select id="selectTaskAlreadyDisposeCountByUserSid" resultType="java.lang.Long">
        SELECT count(distinct a.data_id) FROM data_audit_history a WHERE a.deleted = 0 AND task_sid = #{taskSid}
             AND a.audit_status = 2 AND a.creator = #{userSid}
             AND NOT EXISTS (SELECT b.sid  FROM data_audit_history b WHERE b.deleted = 0 AND b.data_id=a.data_id AND b.audit_status = 3 AND b.create_time>a.create_time );
    </select>

    <select id="selectTodayDisposeCountByUserSid" resultType="java.lang.Long">
        SELECT count(distinct a.data_id) FROM data_audit_history a WHERE a.deleted = 0
        AND a.audit_status = 2 AND a.creator = #{userSid} AND a.create_time  &gt;= #{todayTime,jdbcType=TIMESTAMP}
        AND NOT EXISTS (SELECT b.sid  FROM data_audit_history b WHERE b.deleted = 0 AND b.data_id=a.data_id AND b.audit_status = 3 AND b.create_time>a.create_time );
    </select>

    <select id="selectRejectCountByUserSid" resultType="java.lang.Long" parameterType="java.lang.String">
        SELECT COUNT(1) FROM data_audit_history a WHERE a.deleted = 0
            AND a.audit_status = 2 AND a.creator = #{userSid}
            AND EXISTS (SELECT b.sid FROM data_audit_history b WHERE b.deleted = 0 AND b.data_id=a.data_id AND b.audit_status = 3 AND b.create_time>a.create_time );
    </select>

    <select id="selectNotDispatchDataCount" resultType="java.lang.Long">
        SELECT COUNT(1) FROM data_audit_history WHERE deleted = 0 AND is_newest = 0 AND audit_status != 0 AND data_id IN
        <foreach collection="dataIds" open="(" separator="," close=")"  item="id">
            #{id}
        </foreach>
    </select>

    <select id="selectNotRejectDataCount" resultType="java.lang.Long">
        SELECT COUNT(1) FROM data_audit_history WHERE deleted = 0 AND is_newest = 0 AND audit_status != 1 AND data_id IN
        <foreach collection="dataIds" open="(" separator="," close=")"  item="id">
            #{id}
        </foreach>
    </select>

    <select id="selectNotPassDataCount" resultType="java.lang.Long">
        SELECT COUNT(1) FROM data_audit_history WHERE deleted = 0 AND is_newest = 0 AND audit_status != 2 AND data_id IN
        <foreach collection="dataIds" open="(" separator="," close=")"  item="id">
            #{id}
        </foreach>
    </select>

    <select id="selectAwaitTwoAuditDataCount" resultType="java.lang.Long">
        SELECT COUNT(1) FROM data_audit_history WHERE deleted = 0 AND is_newest = 0 AND audit_status = 2 AND data_id IN
        <foreach collection="dataIds" open="(" separator="," close=")"  item="id">
            #{id}
        </foreach>
    </select>

    <update id="updateIsNewestByDataIds" >
        update data_audit_history set is_newest = 1 where deleted = 0 and is_newest = 0 and data_id in
        <foreach collection="dataIds" open="(" separator="," close=")"  item="id">
            #{id}
        </foreach>
    </update>

    <update id="deleteByDataIds" >
        update data_audit_history set deleted = 1 where deleted = 0 and data_id in
        <foreach collection="dataIds" open="(" separator="," close=")"  item="id">
            #{id}
        </foreach>
    </update>
</mapper>
