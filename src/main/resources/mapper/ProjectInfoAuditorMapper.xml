<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bot.data.collection.server.mapper.ProjectInfoAuditorMapper">


  <update id="deleteBatchByFirstAuditorUserSids" >
    update project_info_auditor set deleted=1,updater=#{updater}
    where project_info_sid=#{projectSid} and type = 0 and deleted = 0 and user_sid in
    <foreach collection="userSids" open="(" separator="," close=")"  item="sid">
      #{sid}
    </foreach>
  </update>

  <update id="deleteBatchByFirstAuditorUserGroupSids" >
    update project_info_auditor set deleted=1,updater=#{updater}
    where project_info_sid=#{projectSid} and type = 0 and deleted = 0 and user_group_sid in
    <foreach collection="userGroupSids" open="(" separator="," close=")"  item="sid">
      #{sid}
    </foreach>
  </update>

  <update id="deleteBatchByTwoAuditorUserSids" >
    update project_info_auditor set deleted=1,updater=#{updater}
    where project_info_sid=#{projectSid} and type = 1 and deleted = 0 and user_sid in
    <foreach collection="userSids" open="(" separator="," close=")"  item="sid">
      #{sid}
    </foreach>
  </update>

  <update id="deleteBatchByTwoAuditorUserGroupSids" >
    update project_info_auditor set deleted=1,updater=#{updater}
    where project_info_sid=#{projectSid} and type = 1 and deleted = 0 and user_group_sid in
    <foreach collection="userGroupSids" open="(" separator="," close=")"  item="sid">
      #{sid}
    </foreach>
  </update>
</mapper>
