<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bot.data.collection.server.mapper.CollectionTaskMapper">

    <resultMap id="BaseResultMap" type="com.bot.data.collection.server.model.entity.CollectionTaskDO">
            <id property="sid" column="sid" jdbcType="VARCHAR"/>
            <result property="sno" column="sno" jdbcType="VARCHAR"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="piSid" column="pi_sid" jdbcType="VARCHAR"/>
            <!--<result property="piSno" column="pi_sno" jdbcType="VARCHAR"/>
            <result property="piType" column="pi_type" jdbcType="INTEGER"/>-->
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="ciNames" column="ci_names" jdbcType="VARCHAR"/>
            <result property="ciCodePath" column="ci_code_path" jdbcType="VARCHAR"/>
            <result property="cycleType" column="cycle_type" jdbcType="TINYINT"/>
            <result property="from" column="from" jdbcType="TINYINT"/>
            <result property="source" column="source" jdbcType="TINYINT"/>
            <result property="pagePath" column="page_path" jdbcType="VARCHAR"/>
            <result property="link" column="link" jdbcType="VARCHAR"/>
            <result property="operatorSid" column="operator_sid" jdbcType="VARCHAR"/>
            <result property="startTime" column="start_time" jdbcType="TIMESTAMP"/>
            <result property="endTime" column="end_time" jdbcType="TIMESTAMP"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
            <result property="needFormatLibrary" column="need_format_library" jdbcType="TINYINT"/>
            <result property="ext" column="ext" jdbcType="VARCHAR"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updater" column="updater" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="deleted" column="deleted" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        sid,sno,name,
        pi_sid,pi_sno,pi_type,
        remark,ci_names,ci_code_path,
        cycle_type,from,source,
        page_path,link,operator_sid,
        start_time,end_time,status,
        need_format_library,ext,creator,
        create_time,updater,update_time,
        deleted
    </sql>

    <resultMap id="selectCollectionTaskPageResultMap" type="com.bot.data.collection.server.model.entity.CollectionTaskDO">
        <id property="sid" column="sid" jdbcType="VARCHAR"/>
        <result property="sno" column="sno" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="ciNames" column="ci_names" jdbcType="VARCHAR"/>
        <result property="project" column="project" jdbcType="VARCHAR"/>
        <result property="taskStatus" column="task_status" jdbcType="TINYINT"/>
        <result property="updaterName" column="updater_name" jdbcType="VARCHAR"/>
        <result property="initialAddTime" column="initial_add_time" jdbcType="TIMESTAMP"/>
        <result property="initialAddTime" column="newest_add_time" jdbcType="TIMESTAMP"/>
    </resultMap>


<!--
    ,
    cycle_type,from,source,
    page_path,link,operator_sid,
    start_time,end_time,status,
    need_format_library,ext,creator,
    create_time,updater,update_time,-->

    <select id="selectTaskPage" parameterType="com.bot.data.collection.server.model.req.CollectionTaskPageReq" resultMap="selectCollectionTaskPageResultMap">
        SELECT sid,sno,name,ci_names,project,task_status,initial_add_time,newest_add_time,updater_name FROM `collection_task` WHERE pi_sid IN(
            SELECT project_info_sid FROM project_info_auditor WHERE type = #{reqVO.type} AND user_sid = #{reqVO.userSid} AND deleted = 0
            UNION
            SELECT project_info_sid FROM project_info_auditor WHERE type = #{reqVO.type} AND user_group_sid IN(
                SELECT user_group_sid FROM system_user_user_group WHERE user_sid = #{reqVO.userSid} AND deleted = 0
            ) AND deleted = 0
        ) AND deleted = 0
        <include refid="selecPage_conditions"/>
        ORDER BY create_time DESC
    </select>

    <sql id="selecPage_conditions">
        <if test="reqVO.sno != null and reqVO.sno != ''">
            and sno like concat('%', #{reqVO.sno}, '%')
        </if>
        <if test="reqVO.name != null and reqVO.name != ''">
            and `name` like concat('%', #{reqVO.name}, '%')
        </if>
        <if test="reqVO.ciNames != null and reqVO.ciNames != ''">
            and ci_names like concat('%', #{reqVO.ciNames}, '%')
        </if>
        <if test="reqVO.project != null and reqVO.project != ''">
            and project like concat('%', #{reqVO.project}, '%')
        </if>

        <if test="reqVO.taskStatus != null">
            and `task_status` = #{reqVO.taskStatus}
        </if>

        <if test="reqVO.initialAddTimeStart != null">
            and initial_add_time &gt;= #{reqVO.initialAddTimeStart,jdbcType=TIMESTAMP},
        </if>
        <if test="reqVO.initialAddTimeEnd != null">
            and initial_add_time &lt; #{reqVO.initialAddTimeEnd,jdbcType=TIMESTAMP},
        </if>

        <if test="reqVO.newestAddTimeStart != null">
            and newest_add_time &gt;= #{reqVO.newestAddTimeStart,jdbcType=TIMESTAMP},
        </if>
        <if test="reqVO.newestAddTimeEnd != null">
            and newest_add_time &lt; #{reqVO.newestAddTimeEnd,jdbcType=TIMESTAMP},
        </if>
    </sql>
</mapper>
