<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bot.data.collection.system.mapper.UserRoleMapper">


  <update id="deleteBatchByUserSids" >
    update system_user_role set deleted=1,updater=#{updater}
    where role_sid=#{roleSid} and deleted = 0 and user_sid in
    <foreach collection="userSids" open="(" separator="," close=")"  item="sid">
      #{sid}
    </foreach>
  </update>
</mapper>
