<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bot.data.collection.system.mapper.MenuMapper">

    <resultMap id="BaseResultMap" type="com.bot.data.collection.system.model.entity.MenuDO">
            <id property="sid" column="sid" jdbcType="BIGINT"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="permission" column="permission" jdbcType="VARCHAR"/>
            <result property="type" column="type" jdbcType="TINYINT"/>
            <result property="sort" column="sort" jdbcType="INTEGER"/>
            <result property="parentSid" column="parent_id" jdbcType="BIGINT"/>
            <result property="path" column="path" jdbcType="VARCHAR"/>
            <result property="icon" column="icon" jdbcType="VARCHAR"/>
            <result property="component" column="component" jdbcType="VARCHAR"/>
            <result property="componentName" column="component_name" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
            <result property="visible" column="visible" jdbcType="BIT"/>
            <result property="keepAlive" column="keep_alive" jdbcType="BIT"/>
            <result property="alwaysShow" column="always_show" jdbcType="BIT"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updater" column="updater" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="deleted" column="deleted" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,name,permission,
        type,sort,parent_id,
        path,icon,component,
        component_name,status,visible,
        keep_alive,always_show,creator,
        create_time,updater,update_time,
        deleted
    </sql>



    <resultMap id="getTreeMap" type="com.bot.data.collection.system.model.rsp.MenuResq">
        <id property="sid" column="sid" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="permission" column="permission" jdbcType="VARCHAR"/>
        <result property="parentSid" column="parent_sid" jdbcType="VARCHAR"/>
        <result property="path" column="path" jdbcType="VARCHAR"/>
        <result property="icon" column="icon" jdbcType="VARCHAR"/>
        <result property="componentName" column="component_name" jdbcType="VARCHAR"/>
        <result property="sort" column="sort" jdbcType="INTEGER"/>
        <result property="visible" column="visible" jdbcType="BIT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>

        <!--<result property="type" column="type" jdbcType="TINYINT"/>
        <result property="sort" column="sort" jdbcType="INTEGER"/>
        <result property="parentId" column="parent_id" jdbcType="BIGINT"/>
        <result property="path" column="path" jdbcType="VARCHAR"/>
        <result property="icon" column="icon" jdbcType="VARCHAR"/>
        <result property="component" column="component" jdbcType="VARCHAR"/>
        <result property="componentName" column="component_name" jdbcType="VARCHAR"/>
        <result property="visible" column="visible" jdbcType="BIT"/>
        <result property="keepAlive" column="keep_alive" jdbcType="BIT"/>
        <result property="alwaysShow" column="always_show" jdbcType="BIT"/>-->
        <!--<result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updater" column="updater" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>-->
        <collection property="children" ofType="com.bot.data.collection.system.model.rsp.MenuResq" >
            <id property="sid" column="tsid" jdbcType="VARCHAR"/>
            <result property="name" column="tname" jdbcType="VARCHAR"/>
            <result property="permission" column="tper" jdbcType="VARCHAR"/>
            <result property="parentSid" column="tparentId" jdbcType="VARCHAR"/>
            <result property="path" column="t_path" jdbcType="VARCHAR"/>
            <result property="icon" column="t_icon" jdbcType="VARCHAR"/>
            <result property="componentName" column="t_component_name" jdbcType="VARCHAR"/>
            <result property="sort" column="t_sort" jdbcType="INTEGER"/>
            <result property="visible" column="t_visible" jdbcType="BIT"/>
            <result property="createTime" column="t_create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="t_update_time" jdbcType="TIMESTAMP"/>
            <collection property="children" ofType="com.bot.data.collection.system.model.rsp.MenuResq" >
                <id property="sid" column="ssid" jdbcType="VARCHAR"/>
                <result property="name" column="sname" jdbcType="VARCHAR"/>
                <result property="permission" column="sper" jdbcType="VARCHAR"/>
                <result property="parentSid" column="sparentId" jdbcType="VARCHAR"/>
                <result property="path" column="s_path" jdbcType="VARCHAR"/>
                <result property="icon" column="s_icon" jdbcType="VARCHAR"/>
                <result property="componentName" column="s_component_name" jdbcType="VARCHAR"/>
                <result property="sort" column="s_sort" jdbcType="INTEGER"/>
                <result property="visible" column="s_visible" jdbcType="BIT"/>
                <result property="createTime" column="s_create_time" jdbcType="TIMESTAMP"/>
                <result property="updateTime" column="s_update_time" jdbcType="TIMESTAMP"/>
            </collection>
        </collection>
    </resultMap>

    <select id="selectMenuTree" resultMap="getTreeMap">
        SELECT
            a.sid,
            a.`name`,
            a.permission,
            a.parent_sid,
            a.path path,
            a.icon icon,
            a.component_name component_name,
            a.sort sort,
            a.visible visible,
            a.create_time create_time,
            a.update_time update_time,
            t.sid tsid,
            t.`name` tname,
            t.permission tper,
            t.parent_sid tparentId,
            t.path t_path,
            t.icon t_icon,
            t.component_name t_component_name,
            t.sort t_sort,
            t.visible t_visible,
            t.create_time t_create_time,
            t.update_time t_update_time,
            s.sid ssid,
            s.`name` sname,
            s.permission sper,
            s.parent_sid sparentId,
            s.path s_path,
            s.icon s_icon,
            s.component_name s_component_name,
            s.sort s_sort,
            s.visible s_visible,
            s.create_time s_create_time,
            s.update_time s_update_time
        FROM
            ( SELECT * FROM system_menu WHERE parent_sid = '0' AND `status` = 0 AND deleted = 0 ) a
                LEFT JOIN system_menu t ON a.sid = t.parent_sid
                AND t.`status` = 0
                AND t.deleted = 0
                LEFT JOIN system_menu s ON t.sid = s.parent_sid
                AND s.`status` = 0
                AND s.deleted = 0
        ORDER BY sort,t_sort,s_sort
    </select>

    <select id="selectMenuSidList" resultType="java.lang.String">
        SELECT DISTINCT menu_sid FROM system_role_menu WHERE role_sid IN(
            SELECT role_sid FROM system_user_role WHERE user_sid = #{userSid} AND deleted = 0
            UNION
            SELECT role_sid FROM system_user_group_role WHERE user_group_sid IN (SELECT user_group_sid FROM system_user_user_group WHERE user_sid = #{userSid} AND deleted = 0) AND deleted = 0
        ) AND deleted = 0
    </select>

    <resultMap id="getTreeInfoMap" type="com.bot.data.collection.system.model.dto.MenuInfoDTO">
        <id property="sid" column="a_sid" jdbcType="VARCHAR"/>
        <result property="name" column="a_name" jdbcType="VARCHAR"/>
        <collection property="children" ofType="com.bot.data.collection.system.model.rsp.MenuResq" >
            <id property="sid" column="t_sid" jdbcType="VARCHAR"/>
            <result property="name" column="t_name" jdbcType="VARCHAR"/>
            <collection property="children" ofType="com.bot.data.collection.system.model.rsp.MenuResq" >
                <id property="sid" column="s_sid" jdbcType="VARCHAR"/>
                <result property="name" column="s_name" jdbcType="VARCHAR"/>
            </collection>
        </collection>
    </resultMap>

    <select id="selectMenuInfoTree" resultMap="getTreeInfoMap">
        SELECT
            a.sid a_sid,
            a.`name` a_name,
            t.sid t_sid,
            t.`name` t_name,
            s.sid s_sid,
            s.`name` s_name
        FROM
            ( SELECT sid, `name`,sort FROM system_menu WHERE parent_sid = '0' AND `status` = 0 AND deleted = 0 ) a
                LEFT JOIN system_menu t ON a.sid = t.parent_sid
                AND t.`status` = 0
                AND t.deleted = 0
                LEFT JOIN system_menu s ON t.sid = s.parent_sid
                AND s.`status` = 0
                AND s.deleted = 0
        ORDER BY a.sort,t.sort,s.sort
    </select>

    <select id="selectMenuSidListByRoleSid" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT menu_sid FROM system_role_menu WHERE role_sid = #{groupSid} and deleted = 0
        --         and `status` = 0
    </select>
</mapper>
