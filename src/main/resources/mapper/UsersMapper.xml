<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bot.data.collection.system.mapper.UserMapper">

    <resultMap id="BaseResultMap" type="com.bot.data.collection.system.model.entity.UserDO">
            <id property="sid" column="sid" jdbcType="VARCHAR"/>
            <result property="username" column="username" jdbcType="VARCHAR"/>
            <result property="password" column="password" jdbcType="VARCHAR"/>
            <result property="email" column="email" jdbcType="VARCHAR"/>
            <result property="mobile" column="mobile" jdbcType="VARCHAR"/>
            <result property="address" column="address" jdbcType="VARCHAR"/>
            <result property="zipCode" column="zip_code" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
            <result property="loginIp" column="login_ip" jdbcType="VARCHAR"/>
            <result property="loginDate" column="login_date" jdbcType="TIMESTAMP"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updater" column="updater" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        sid,username,password,email,mobile,
        address,zip_code,remark,status,
        login_ip,login_date,creator,
        create_time,updater,update_time
    </sql>

    <select id="selectGroupUserList" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT  <include refid="Base_Column_List"/>  FROM system_users WHERE sid in (SELECT user_sid FROM system_user_user_group WHERE user_group_sid = #{groupSid} and deleted = 0)
        and deleted = 0
--         and `status` = 0
    </select>

    <select id="selectUserSidListByGroupSid" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT user_sid FROM system_user_user_group WHERE user_group_sid = #{groupSid} and deleted = 0
        --         and `status` = 0
    </select>

    <select id="selectUserSidListByRoleSid" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT user_sid FROM system_user_role WHERE role_sid = #{roleSid} and deleted = 0
        --         and `status` = 0
    </select>

</mapper>
