<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bot.data.collection.system.mapper.UserGroupMapper">

    <!--<resultMap id="BaseResultMap" type="entity.model.system.com.bot.data.collection.UserGroupDO">
            <id property="sid" column="sid" jdbcType="BIGINT"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
            <result property="parentId" column="parent_id" jdbcType="BIGINT"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updater" column="updater" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="deleted" column="deleted" jdbcType="BIT"/>
    </resultMap>-->

    <sql id="Base_Column_List">
        id,name,remark,
        status,parent_id,creator,
        create_time,updater,update_time,
        deleted
    </sql>

   <!-- <select id="selectRoleUserGroupPage" parameterType="req.model.system.com.bot.data.collection.RoleUserGroupPageReq" resultMap="BaseResultMap">
        SELECT  <include refid="Base_Column_List"/>  FROM system_user_group WHERE id in (SELECT user_group_id FROM system_user_group_role WHERE role_id = #{reqVO.roleId} and deleted = 0)
        and `status` = 0 and deleted = 0
        <include refid="selecGroupUser_conditions"></include>
    </select>

    <select id="selectNoRoleUserGroupPage" parameterType="req.model.system.com.bot.data.collection.RoleUserGroupPageReq" resultMap="BaseResultMap">
        SELECT  <include refid="Base_Column_List"/>  FROM system_user_group WHERE id in (SELECT user_group_id FROM system_user_group_role WHERE role_id != #{reqVO.roleId} and deleted = 0)
        and `status` = 0 and deleted = 0
        <include refid="selecGroupUser_conditions"></include>
    </select>-->

    <sql id="selecGroupUser_conditions">
        <if test="reqVO.userGroupName != null and reqVO.userGroupName != ''">
            and `name` like concat('%', #{reqVO.userGroupName}, '%')
        </if>

    </sql>

    <select id="selectUserGroupSidListByRoleSid" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT user_group_sid FROM system_user_group_role WHERE role_sid = #{roleSid} and deleted = 0
        --         and `status` = 0
    </select>
</mapper>
