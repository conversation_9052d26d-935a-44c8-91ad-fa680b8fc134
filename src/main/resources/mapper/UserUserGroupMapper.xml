<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bot.data.collection.system.mapper.UserUserGroupMapper">


  <update id="deleteBatchByUserSids" >
    update system_user_user_group set deleted=1,updater=#{updater}
    where user_group_sid=#{groupSid} and deleted = 0 and user_sid in
    <foreach collection="userSids" open="(" separator="," close=")"  item="sid">
      #{sid}
    </foreach>
  </update>
</mapper>
