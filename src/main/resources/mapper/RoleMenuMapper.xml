<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bot.data.collection.system.mapper.RoleMenuMapper">

  <update id="deleteBatchByMenuSids" >
    update system_role_menu set deleted=1,updater=#{updater}
    where role_sid=#{roleSid} and deleted = 0 and menu_sid in
    <foreach collection="menuSids" open="(" separator="," close=")"  item="sid">
      #{sid}
    </foreach>
  </update>
</mapper>
