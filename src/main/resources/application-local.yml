app:
  datasource:
    host: *************:8010
    username: root
    password: 123456
    name: bot_collection_data_localtest
#    host: dev-hw.botsmart.cn:33006
#    username: root
#    password: Bots<PERSON>@2
#    name: bot_collection_data_localtest
  redis:
    host: 127.0.0.1
    database: 0
    port: 6379
    password: foobared
#    host: **************
#    database: 1
#    port: 7675
#    password: Botsmart.cn
#    host: ***************
#    database: 1
#    port: 60379
#    password: Botsmart.cn
#  rabbitmq:
#    host: *************
#    port: 8012
#    username: guest
#    password: guest
#    host: **************
#    port: 7672
#    username: saasuser
#    password: botsmart.cn
#    host: ***************
#    port: 5672
#    username: saasuser
#    password: botsmart.cn
  elasticsearch:
    #    host: *************
    #    port: 9200
    #    username: elastic
    #    password: 123456
    host: es-cluster-002.botsmart.cn
    port: 29200
    username: elastic
    password: 02_Qm90c21hcnQ$
#  elasticsearch:
#    host: *************
#    port: 9200
#    username: elastic
#    password: 123456
#    host: es-cluster-002.botsmart.cn
#    port: 29200
#    username: elastic
#    password: 02_Qm90c21hcnQ$
#    host: es-110.miyun.botsmart.cn
#    port: 19528
#    username: elastic
#    password: Btzn@2022
#    host: es-110.miyun.botsmart.cn
#    port: 19529
#    username: elastic
#    password: Btzn@2023
#  minio:
##    port: 9000
##    endPoint: http://************* #Minio服务所在地址
##    bucketName: botsmart #存储桶名称
##    accessKey: HL8UixRN7fpxo9pEPCIW #访问的key
##    secretKey: xQu5pQEhHXjW5kRDHkltYoaNuIE5Zb2wUWGiLuBd #访问的秘钥
##    collection-result-img-folder: col_result_img/
#    port: 9000
#    endPoint: http://minio.miyun.botsmart.cn #Minio服务所在地址
#    bucketName: botsmart #存储桶名称
#    accessKey: YuN2vUABOkg6uMl0 #访问的key
#    secretKey: GUMqhs0lHBwHrXHt84cxyj2kME6HeYyX
#    collection-result-img-folder: col_result_img/
#custom:
#  xh-wx-official-accounts-add-task-url: http://127.0.0.1:8200/test/addTask
mybatis-plus:
  mapper-locations: classpath:/mapper/*.xml
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
#    map-underscore-to-camel-case: true