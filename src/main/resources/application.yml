spring:
  profiles:
    active: @profile.active@
  application:
    name: bot-collection-data
  servlet:
    multipart:
      enabled: true
      maxFileSize: 2048MB
      maxRequestSize: 2048MB
  datasource:
    name: ${app.datasource.name}
    url: jdbc:mysql://${app.datasource.host}/${app.datasource.name}?allowMultiQueries=true&useUnicode=true&characterEncoding=utf-8&useSSL=false&serverTimezone=Asia/Shanghai
    username: ${app.datasource.username}
    password: ${app.datasource.password}
    hikari:
      connectionTimeout: 60000
      maximumPoolSize: 20
      validation-timeout: 30000
      max-lifetime: 60000
      minimum-idle: 10
      idle-timeout: 60000
    # 数据源选择
#    type: com.alibaba.druid.pool.DruidDataSource
#    # clickhouse配置
#    click:
#      driverClassName: ru.yandex.clickhouse.ClickHouseDriver
#      url: ********************************************
#      username: default
#      password:
#      initialSize: 10
#      maxActive: 100
#      minIdle: 10
#      maxWait: 6000
#  rabbitmq:
#    host: ${app.rabbitmq.host}
#    port: ${app.rabbitmq.port}
#    username: ${app.rabbitmq.username}
#    password: ${app.rabbitmq.password}
#    listener:
#      direct:
#        acknowledge-mode: manual
#        prefetch: 1
#        concurrency: 5 #消费者最小数量
#        max-concurrency: 20
  redis:
    database: ${app.redis.database} # Redis数据库索引（默认为0）
    host: ${app.redis.host} # Redis服务器地址
    port: ${app.redis.port} # Redis服务器连接端口
    password: ${app.redis.password} # Redis服务器连接密码（默认为空)
    timeout: 3000ms # 链接超时时间 单位 ms（毫秒）
    lettuce:
      pool:
        max-active: 8 # 连接池最大连接数（使用负值表示没有限制） 默认 8
        max-wait: -1ms # 连接池最大阻塞等待时间（使用负值表示没有限制） 默认 -1
        max-idle: 8 # 连接池最大连接数（使用负值表示没有限制） 默认 8
        min-idle: 0
management:
  endpoints:
    web:
      base-path: /monitor
      path-mapping:
        health: /health
    health:
      show-details: always
  health:
    checks:
      enabled: true
      restart:
        required:
          enabled: true
#minio:
#  port: ${app.minio.port}
#  endPoint: ${app.minio.endPoint} #Minio服务所在地址
#  bucketName: ${app.minio.bucketName} #存储桶名称
#  accessKey: ${app.minio.accessKey} #访问的key
#  secretKey: ${app.minio.secretKey} #访问的秘钥
#  collection-result-img-folder: ${app.minio.collection-result-img-folder}
elasticsearch:
  host: ${app.elasticsearch.host}
  port: ${app.elasticsearch.port}
  username: ${app.elasticsearch.username}
  password: ${app.elasticsearch.password}
  connTimeout: 30000
  socketTimeout: 60000
  connectionRequestTimeout: 5000
  max-conn-total: 50
  max-conn-per-route: 50

custom:
  xh-wx-official-accounts-add-task-url: ${custom.xh-wx-official-accounts-add-task-url}

server:
  port: 8212
knife4j:
  enable: true
  documents:
    -
      group: 2.X版本
      name: 接口签名
      locations: classpath:sign/*
  setting:
    language: zh-CN
    enableSwaggerModels: true
    enableDocumentManage: true
    swaggerModelName: 实体类列表
    enableVersion: false
    enableReloadCacheParameter: false
    enableAfterScript: true
    enableFilterMultipartApiMethodType: POST
    enableFilterMultipartApis: false
    enableRequestCache: true
    enableHost: false
    enableHostText: *************:8005
    enableHomeCustom: true
    homeCustomLocation: classpath:markdown/home.md
    enableSearch: false
    enableFooter: false
    enableFooterCustom: true
    footerCustomContent: 内部使用违规必究
    enableDynamicParameter: false
    enableDebug: true
    enableOpenApi: false
    enableGroup: true
  cors: false
  production: false
  basic:
    enable: true
    username: botSmart.cn
    password: botSmart.cn
#yudao:
#  security:
#    token-header: Authorization
#    token-secret: abcdefghijklmnopqrstuvwxyz
#    token-timeout: 1d
#    session-timeout: 30m
#    mock-enable: true
#    mock-secret: test
#    permit-all_urls:
#      - /admin-api/mp/open/** # 微信公众号开放平台，微信回调接口，不需要登录