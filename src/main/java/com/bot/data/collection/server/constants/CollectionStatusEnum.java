package com.bot.data.collection.server.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum CollectionStatusEnum {
    RUNNING(0, "采集中"),
    ENDED(1, "采集结束"),
    //DELETE(-1, "删除标志"),执行和停止
    /*STOP(0, "采集准备"),
    READY(1, "待采集"),
    RUNNING(2, "采集中"),
    ENDED(3, "采集结束"),
    PAUSE(4, "暂停采集"),*/
    UNKNOWN(-99, "未知");;

    private final int code;
    private final String message;

    public static int getCode(String message){
        return Stream.of(values()).filter(b -> b.message.equals(message)).findFirst().orElse(UNKNOWN).getCode();
    }
    public static String getValue(int code){
        return Stream.of(values()).filter(b -> b.code==code).findFirst().orElse(UNKNOWN).getMessage();
    }
}
