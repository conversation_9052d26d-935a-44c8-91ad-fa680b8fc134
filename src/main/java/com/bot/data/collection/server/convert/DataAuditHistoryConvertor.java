package com.bot.data.collection.server.convert;

import com.bot.data.collection.server.model.dto.DataAuditHistoryDTO;
import com.bot.data.collection.server.model.entity.DataAuditHistoryDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

@Mapper(componentModel = "spring")
public interface DataAuditHistoryConvertor {
    @Mappings({
            @Mapping(source = "creatorName", target = "operator"),
            @Mapping(source = "createTime", target = "operateTime")
    })
    DataAuditHistoryDTO convert(DataAuditHistoryDO obj);
    List<DataAuditHistoryDTO> convert(List<DataAuditHistoryDO> obj);
}
