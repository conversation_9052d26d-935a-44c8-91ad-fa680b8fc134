package com.bot.data.collection.server.model.rsp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value="用户数据计数对象",description="用户数据汇总计数对象")
@Data
public class UserDataCountRsp {


    @ApiModelProperty(value="111",name="待处理总条数")
    private Long awaitDisposeCount;
    @ApiModelProperty(value="111",name="已处理总条数")
    private Long alreadyDisposeCount;
    @ApiModelProperty(value="111",name="今日处理条数")
    private Long todayDisposeCount;
    @ApiModelProperty(value="111",name="被打回次数")
    private Long rejectCount;

}
