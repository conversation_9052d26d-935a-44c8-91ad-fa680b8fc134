package com.bot.data.collection.server.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.bot.data.collection.common.pojo.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 项目信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("project_info")
public class ProjectInfoDO extends BaseDO {
    //sid
    @TableId(value = "sid")
    private String sid;
    //项目编号
    @TableField(value = "pi_sno")
    private String piSno;
    //项目名称
    @TableField(value = "pi_name")
    private String piName;
    //项目说明
    @TableField(value = "pi_desc")
    private String piDesc;
    //项目类型 0-内部数据 1-业务数据 2-转出数据
    @TableField(value = "pi_type")
    private Integer piType;

    /**
     * 是否人审：0-否，1-是 people_audit
     */
    @TableField(value = "people_audit")
    private Boolean peopleAudit;
    //项目es存储结构
    @TableField(value = "pi_mapper")
    private String piMapper;
    @TableField(value = "pi_mapper_dict")
    private String piMapperDict;
    //操作人sid
    /*@TableField(value = "pi_operator_sid")
    private String piOperatorSid;
    //创建时间
    @TableField(value = "pi_create_time", insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private LocalDateTime piCreateTime;*/
    //创建人name
    @TableField(value = "creator_name")
    private String creatorName;

}
