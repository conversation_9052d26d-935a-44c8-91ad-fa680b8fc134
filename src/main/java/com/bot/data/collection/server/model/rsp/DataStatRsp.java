package com.bot.data.collection.server.model.rsp;

import com.bot.data.collection.server.model.es.EsBucketDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel(value="任务汇总对象",description="任务汇总对象")
@Data
public class DataStatRsp {

    @ApiModelProperty(value="11111",name="任务sid",required=true)
    private String sid;
    @ApiModelProperty(value="11111",name="任务标识",required=true)
    private String sno;
    @ApiModelProperty(value="11111",name="任务名称")
    private String name;
    @ApiModelProperty(name="关联项目名称/编号")
    private String project;
    @ApiModelProperty(name="任务分类")
    private String ciNames;


    @ApiModelProperty(name="采集数据统计")
    private List<EsBucketDTO> stats;



}
