package com.bot.data.collection.server.controller;

import com.bot.data.collection.common.exception.ServiceException;
import com.bot.data.collection.common.pojo.CommonResult;
import com.bot.data.collection.common.pojo.PageResult;
import com.bot.data.collection.server.model.req.CollectionTaskPageReq;
import com.bot.data.collection.server.model.req.CollectionTaskCreateReq;
import com.bot.data.collection.server.model.req.CollectionTaskUpdateReq;
import com.bot.data.collection.server.model.rsp.CollectionTaskInfoRsp;
import com.bot.data.collection.server.model.rsp.CollectionTaskRsp;
import com.bot.data.collection.server.service.CollectionTaskService;
import com.bot.data.collection.system.constants.SystemErrorEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

@Api(tags = "采集任务管理")
@RestController
@RequestMapping("/task")
@Validated
public class CollectionTaskController {

    @Resource
    CollectionTaskService collectionTaskService;
    @PostMapping("/page")
    @ApiOperation(value = "获得采集任务分页列表")
    //@PermitAll
    //@PreAuthorize("@ss.hasPermission('system:user:list')")
    public CommonResult<PageResult<CollectionTaskRsp>> getCollectionTaskPage(@Valid @RequestBody CollectionTaskPageReq reqVO) {
        // 获得用户分页列表 CollectionTaskService
        return CommonResult.success(collectionTaskService.getCollectionTaskPage(reqVO));
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得采集任务信息详情")
    //@PermitAll
    //@PreAuthorize("@ss.hasPermission('system:user:query')")
    public CommonResult<CollectionTaskInfoRsp> getCollectionTaskInfo(@ApiParam(name = "sid", value = "123", required = true) @RequestParam("sid") String sid) {
        if (StringUtils.isBlank(sid)) {
            throw new ServiceException(SystemErrorEnum.PARAM_IS_NULL);
        }
        return CommonResult.success(collectionTaskService.getCollectionTaskInfo(sid));
    }

    @PostMapping("/create")
    @ApiOperation(value = "新增采集任务信息")
    //@PermitAll
    //@PreAuthorize("@ss.hasPermission('system:user:create')")
    public CommonResult<String> createCollectionTaskInfo(@Valid @RequestBody CollectionTaskCreateReq reqVO) {
        return CommonResult.success(collectionTaskService.createCollectionTaskInfo(reqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改采集任务信息")
    //@PermitAll
    //@PreAuthorize("@ss.hasPermission('system:user:update')")
    public CommonResult<Boolean> updateCollectionTaskInfo(@Valid @RequestBody CollectionTaskUpdateReq reqVO) {
        collectionTaskService.updateCollectionTaskInfo(reqVO);
        return CommonResult.success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除采集任务信息")
    //@PermitAll
    //@PreAuthorize("@ss.hasPermission('system:user:delete')")
    public CommonResult<Boolean> deleteCollectionTaskInfo(@ApiParam(name = "sid", value = "123", required = true) @RequestParam("sid") String sid) {
        if (StringUtils.isBlank(sid)) {
            throw new ServiceException(SystemErrorEnum.PARAM_IS_NULL);
        }
        collectionTaskService.deleteCollectionTaskInfo(sid);
        return CommonResult.success(true);
    }
}
