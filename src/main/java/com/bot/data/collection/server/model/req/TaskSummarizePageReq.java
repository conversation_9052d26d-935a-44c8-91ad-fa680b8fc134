package com.bot.data.collection.server.model.req;

import com.bot.data.collection.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@ApiModel(value="采集任务对象",description="采集任务信息")
@Data
public class TaskSummarizePageReq extends PageParam {
    /*@ApiModelProperty(value="0",name="审核人类型：0-初审人，1-复审人")
    private Integer type;*/
    @ApiModelProperty(name="任务标识")
    private String sno;

    @ApiModelProperty(name="任务名称")
    private String name;

    @Schema(name="任务分类")
    private String ciNames;
//
    @Schema(name="关联项目名称/编号")
    private String project;

    @ApiModelProperty(value="0",name="任务状态：0 -未开始 1 -进行中")
    private Integer taskStatus;

    @ApiModelProperty(value = "2022-07-01 00:00:00", name = "初始新增时间（开始）")
    private LocalDateTime initialAddTimeStart;
    @ApiModelProperty(value = "2022-07-01 00:00:00", name = "初始新增时间（结束）")
    private LocalDateTime initialAddTimeEnd;

    @ApiModelProperty(value = "2022-07-01 00:00:00", name = "最新新增时间（开始）")
    private LocalDateTime newestAddTimeStart;
    @ApiModelProperty(value = "2022-07-01 00:00:00", name = "最新新增时间（开始）")
    private LocalDateTime newestAddTimeEnd;
}
