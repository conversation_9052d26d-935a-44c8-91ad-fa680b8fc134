package com.bot.data.collection.server.model.dto;

import com.bot.data.collection.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@ApiModel(value="采集结构信息对象",description="采集结构信息对象")
@Data
public class DataAuditHistoryDTO {

    //审核状态：0-待派发，1-已派发，2-待复审，3-已打回，4-已通过
    @ApiModelProperty(value="审核状态：0-待派发，1-已派发，2-待复审，3-已打回，4-已通过",name="审核状态",required = true)
    private Integer auditStatus;

    @ApiModelProperty(value="张三",name="操作人")
    private String operator;

    @ApiModelProperty(value = "2022-07-01 00:00:00", name = "操作时间", required = true)
    @JsonFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime operateTime;




}
