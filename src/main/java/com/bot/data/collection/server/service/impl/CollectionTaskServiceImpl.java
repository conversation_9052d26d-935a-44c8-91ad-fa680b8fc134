package com.bot.data.collection.server.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bot.data.collection.common.exception.ServiceException;
import com.bot.data.collection.common.pojo.PageResult;
import com.bot.data.collection.common.utils.UUIDUtils;
import com.bot.data.collection.server.constants.*;
import com.bot.data.collection.server.convert.CollectionTaskConvertor;
import com.bot.data.collection.server.mapper.ClassifyInfoMapper;
import com.bot.data.collection.server.mapper.CollectionTaskMapper;
import com.bot.data.collection.server.model.entity.ClassifyInfoDO;
import com.bot.data.collection.server.model.entity.CollectionTaskDO;
import com.bot.data.collection.server.model.req.CollectionTaskCreateReq;
import com.bot.data.collection.server.model.req.CollectionTaskPageReq;
import com.bot.data.collection.server.model.req.CollectionTaskUpdateReq;
import com.bot.data.collection.server.model.rsp.CollectionTaskInfoRsp;
import com.bot.data.collection.server.model.rsp.CollectionTaskRsp;
import com.bot.data.collection.server.service.CollectionTaskService;
import com.bot.data.collection.system.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class CollectionTaskServiceImpl implements CollectionTaskService {

    @Resource
    CollectionTaskMapper collectionTaskMapper;
    @Resource
    ClassifyInfoMapper classifyInfoMapper;
    @Resource
    CollectionTaskConvertor collectionTaskConvertor;
    @Override
    public PageResult<CollectionTaskRsp> getCollectionTaskPage(CollectionTaskPageReq reqVO) {
        /*int pageNo = reqVO.getPageNo();
        int pageSize = reqVO.getPageSize();
        Page<CollectionTaskRsp> page = new Page<>(pageNo, pageSize);
        List<CollectionTaskRsp> list = collectionTaskMapper.selectCollectionTaskPage(page, reqVO);
        convertListCollectionTaskRsp(list);
        PageResult<CollectionTaskRsp> resp = new PageResult<>();
        resp.setRecords(list);
        resp.setTotal(page.getTotal());*/
        PageResult<CollectionTaskDO> projectInfoPage = collectionTaskMapper.selectPage(reqVO);
        PageResult<CollectionTaskRsp> rsp = collectionTaskConvertor.convertPage(projectInfoPage);
        convertListCollectionTaskRsp(rsp.getRecords());
        return rsp;
    }

    @Override
    public CollectionTaskInfoRsp getCollectionTaskInfo(String sid) {
        CollectionTaskDO collectionTaskDO = collectionTaskMapper.selectById(sid);
        if (collectionTaskDO == null) {
            throw new ServiceException(ServerErrorEnum.DATA_INFO_NOT_EXISTS);
        }
        return collectionTaskConvertor.convert(collectionTaskDO);
    }

    /**
     * 获取分类名称集合
     * @param ciCodePath
     * @return
     */
    private String getCiNames(String ciCodePath){
        String codePathName = "";
        if (StringUtils.isNotEmpty(ciCodePath)){
            String[] ciCodePathArr = ciCodePath.split("_");

            String codePath = "";
            for (int i = 0; i < ciCodePathArr.length; i++) {
                if (i > 0){
                    codePath += "_";
                }
                codePath += ciCodePathArr[i];
                ClassifyInfoDO classifyInfoDO = classifyInfoMapper.selectByCodePath(codePath);
                if (classifyInfoDO != null){
                    if (i > 0){
                        codePathName += "-";
                    }
                    codePathName += classifyInfoDO.getName();
                }
            }
        }
        return codePathName;
    }

    @Override
    public String createCollectionTaskInfo(CollectionTaskCreateReq reqVO) {
        CollectionTaskDO taskDO = collectionTaskConvertor.convert(reqVO);
        String sid = UUIDUtils.getUUID();
        taskDO.setSid(sid);
        taskDO.setSno(UUIDUtils.getSno());
        if (CycleTypeEnum.TIME_INTERVAL.getCode() != reqVO.getCycleType()) {
            taskDO.setEndTime(null);
            /*if (CycleTypeEnum.LONG_SPECIFY.getCode() != reqVO.getCycleType()) {
                taskDO.setStartTime(null);
            }*/
        }
        taskDO.setCreatorName(SecurityUtils.getLoginUser().getName());
        taskDO.setCiNames(getCiNames(taskDO.getCiCodePath()));
        collectionTaskMapper.insert(taskDO);
        return sid;
    }

    @Override
    public void updateCollectionTaskInfo(CollectionTaskUpdateReq reqVO) {
        String sid = reqVO.getSid();
        CollectionTaskDO taskDO = collectionTaskMapper.selectById(sid);
        if (taskDO == null) {
            throw new ServiceException(ServerErrorEnum.DATA_INFO_NOT_EXISTS);
        }
        int status = taskDO.getStatus();
        if (CollectionStatusEnum.ENDED.getCode() != status) {
            //if (CollectionStatusEnum.DELETE.getCode() != status && CollectionStatusEnum.STOP.getCode() != status) {
            throw new ServiceException(ServerErrorEnum.TASK_ALREADY_RUNNING);
        }
        CollectionTaskDO collectionTaskDO = collectionTaskConvertor.convertDO(reqVO);
        taskDO.setCiNames(getCiNames(taskDO.getCiCodePath()));
        collectionTaskMapper.updateById(collectionTaskDO);
    }

    @Override
    public void deleteCollectionTaskInfo(String sid) {
        CollectionTaskDO taskDO = collectionTaskMapper.selectById(sid);
        if (taskDO == null) {
            throw new ServiceException(ServerErrorEnum.DATA_INFO_NOT_EXISTS);
        }
        int status = taskDO.getStatus();
        if (CollectionStatusEnum.ENDED.getCode() != status) {
            //if (CollectionStatusEnum.DELETE.getCode() != status && CollectionStatusEnum.STOP.getCode() != status) {
            throw new ServiceException(ServerErrorEnum.TASK_ALREADY_RUNNING);
        }
        collectionTaskMapper.deleteById(sid);
    }

    private void convertListCollectionTaskRsp(List<CollectionTaskRsp> list) {
        if (CollUtil.isNotEmpty(list)) {
            for (CollectionTaskRsp rsp : list) {
                if (rsp.getFrom() != null) {
                    rsp.setFromValue(CollectionFromEnum.getValue(rsp.getFrom()));
                }
                if (rsp.getSource() != null) {
                    rsp.setSourceValue(CollectionSourceEnum.getValue(rsp.getSource()));
                }
                if (rsp.getCycleType() != null) {
                    rsp.setCycleTypeValue(CycleTypeEnum.getValue(rsp.getCycleType()));
                }
                if (rsp.getStatus() != null) {
                    rsp.setStatusValue(CollectionStatusEnum.getValue(rsp.getStatus()));
                }
            }
        }
    }
}
