package com.bot.data.collection.server.model.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bot.data.collection.common.pojo.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 采集结构模板对象：存储可用的项目信息的映射结构模板
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("collection_flat_template")
public class CollectionFlatTemplateDO extends BaseDO {
    //sid
    @TableId(value = "sid")
    private String sid;
    //项目名称
    @TableField(value = "cft_name")
    private String cftName;
    //项目类型 0-系统模板 1-自定义模板
    @TableField(value = "cft_type")
    private Integer cftType;
    //项目es存储结构
    @TableField(value = "cft_flat")
    private String cftFlat;
    //操作人sid
    /*@TableField(value = "cft_operator_sid")
    private String cftOperatorSid;
    //创建时间
    @TableField(value = "cft_create_time", insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private LocalDateTime cftCreateTime;*/

}
