package com.bot.data.collection.server.model.dto;

import com.bot.data.collection.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@ApiModel(value="分类对象",description="分类信息对象")
@Data
public class ClassDTO {

    @ApiModelProperty(value="111111",name="sid")
    private String sid;

    @ApiModelProperty(value="001",name="分类代码",required = true)
    private String code;

    @ApiModelProperty(value="基础数据",name="分类名称",required = true)
    private String name;

    //子分类代码路径
    @ApiModelProperty(value="001_001_001",name="代码路径")
    private String codePath;

    @ApiModelProperty(value="001",name="父分类id")
    private String parentSid;

    @ApiModelProperty(value = "2022-07-01 00:00:00", name = "创建时间", required = true)
    @JsonFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;
    /*@ApiModelProperty(value="001",name="二级分类id",required = true)
    private String liCiSid;*/

    /*@ApiModelProperty(value="001",name="父分类id",required = true)
    private String parentSid;*/
    @ApiModelProperty(name="子分类对象")
    List<ClassDTO> classDTOList;
}
