package com.bot.data.collection.server.model.base;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
public class CollectionTaskBaseVO {

    //名称
    @ApiModelProperty(value="项目甲",name="任务名称",required=true)
    @NotEmpty(message = "任务名称不能为空")
    private String name;
    @ApiModelProperty(value="项目。。。",name="任务说明")
    private String remark;

    /*@ApiModelProperty(value="001",name="（任务分类）数据分类名称路径",required=true)
    private String ciNames;*/
    @ApiModelProperty(value="1233333001",name="分类sid",required=true)
    @NotEmpty(message = "任务分类不能为空")
    private String ciSid;
    @ApiModelProperty(value="001",name="（任务分类）数据分类编号路径")
    private String ciCodePath;

    @ApiModelProperty(value="1233333001",name="关联项目sid",required=true)
    @NotEmpty(message = "关联项目不能为空")
    private String piSid;
    @ApiModelProperty(value="001",name="项目名称/编号")
    private String project;
    /*@ApiModelProperty(value="1",name="项目类型 0-基础数据 1-业务数据 2-转出数据")
    private Integer piType;*/

    @ApiModelProperty(value="0",name="采集来源（获取方式）：0-app 1-web",required=true)
    @NotNull(message = "采集来源不能为空")
    private Integer from;
    @ApiModelProperty(value="0",name="采集方式（获取来源）：0-本地脚本 1-三方接口（清博）",required=true)
    @NotNull(message = "采集方式不能为空")
    private Integer source;
    //（采集周期）采集类型 0-长期（含历史） 1-长期（指定时间开始） 2-长期（持续更新）
    @ApiModelProperty(value="0",name="采集周期（采集类型）：0-长期采集（含历史） 1-单次采集 2-时间段采集（指定时间开始）",required=true)
    @NotNull(message = "采集周期不能为空")
    private Integer cycleType;

    @ApiModelProperty(value="www.1233333001",name="采集链接或app包名",required=true)
    @NotEmpty(message = "采集链接或app包名不能为空")
    private String link;
    //获取来源 页面获取路径
    @ApiModelProperty(value="项目。。。",name="采集模块路径")
    private String pagePath;
    @ApiModelProperty(value="0",name="状态")
    private Integer status;
}
