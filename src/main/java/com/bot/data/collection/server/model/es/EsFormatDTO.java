package com.bot.data.collection.server.model.es;

import lombok.Data;

@Data
public class EsFormatDTO implements Cloneable {

    private String type;

    private String format;

    @Override
    public EsFormatDTO clone() {
        try {
            EsFormatDTO clone = (EsFormatDTO) super.clone();
            // TODO: copy mutable state here, so the clone can't change the internals of the original
            return clone;
        } catch (CloneNotSupportedException e) {
            throw new AssertionError();
        }
    }
}
