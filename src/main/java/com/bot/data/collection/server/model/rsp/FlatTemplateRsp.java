package com.bot.data.collection.server.model.rsp;

import com.bot.data.collection.server.model.dto.CollectionFlatDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


@ApiModel(value="项目信息对象",description="项目信息")
@Data
public class FlatTemplateRsp {
    @ApiModelProperty(value="11111",name="模板sid")
    private String sid;

    @ApiModelProperty(value="自定义",name="模板名称")
    private String cftName;

    @ApiModelProperty(name="模板结构")
    private List<CollectionFlatDTO> cftFlat;

}
