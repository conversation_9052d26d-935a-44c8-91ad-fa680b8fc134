package com.bot.data.collection.server.model.rsp;

import com.bot.data.collection.common.utils.DateUtils;
import com.bot.data.collection.server.model.base.CollectionTaskBaseVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@ApiModel(value="采集任务对象",description="采集任务信息")
@Data
public class CollectionTaskRsp extends CollectionTaskBaseVO {
    @ApiModelProperty(value="11111",name="项目sid",required=true)
    private String sid;
    @ApiModelProperty(value="11111",name="任务标识",required=true)
    private String sno;
    @ApiModelProperty(name="关联项目名称/编号")
    private String project;
    @ApiModelProperty(name="任务分类")
    private String ciNames;
    //项目类型 0-基础数据 1-业务数据 2-转出数据
    @ApiModelProperty(name="项目类型")
    private String projectTypeValue;
    //采集类型 0-长期（含历史） 1-长期（指定时间开始） 2-长期（持续更新）
    @ApiModelProperty(name="采集周期")
    private String cycleTypeValue;
    //获取方式 0-app 1-web
    @ApiModelProperty(name="获取方式")
    private String fromValue;
    //获取来源 0-本地脚本 1-三方接口（清博）
    @ApiModelProperty(name="获取来源")
    private String sourceValue;

    @ApiModelProperty(name="执行状态")
    private String statusValue;

    @ApiModelProperty(value="zhangsan",name="创建人名称")
    private String creatorName;

    @ApiModelProperty(value = "2022-07-01 00:00:00", name = "创建时间", required = true)
    @JsonFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "2022-07-01 00:00:00", name = "更新时间", required = true)
    @JsonFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime updateTime;
}
