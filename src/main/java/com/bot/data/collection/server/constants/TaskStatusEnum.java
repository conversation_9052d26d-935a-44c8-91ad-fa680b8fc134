package com.bot.data.collection.server.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum TaskStatusEnum {
    NOT_STARTED(0, "未开始"),
    UNDER_WAY(1, "进行中"),
    UNKNOWN(-99, "未知");;

    private final int code;
    private final String message;

    public static int getCode(String message){
        return Stream.of(values()).filter(b -> b.message.equals(message)).findFirst().orElse(UNKNOWN).getCode();
    }
    public static String getValue(int code){
        return Stream.of(values()).filter(b -> b.code==code).findFirst().orElse(UNKNOWN).getMessage();
    }
}
