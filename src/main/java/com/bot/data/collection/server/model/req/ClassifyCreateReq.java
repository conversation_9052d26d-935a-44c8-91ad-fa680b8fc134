package com.bot.data.collection.server.model.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@ApiModel("分类信息创建 Request VO")
@Data
public class ClassifyCreateReq {

    @ApiModelProperty(value="111111",name="父分类sid")
    private String parentSid;

   /* @ApiModelProperty(value="111111",name="主分类sid（二级分类sid）")
    private String liCiSid;*/

    @ApiModelProperty(value="基础数据",name="分类名称",required = true)
    @NotEmpty(message = "基础数据不能为空")
    private String name;

    /*@ApiModelProperty(value="111111",name="父编号路径（二级及更多等级之后的才有）")
    private String parentCodePath;*/
}
