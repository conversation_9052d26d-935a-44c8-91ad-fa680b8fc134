package com.bot.data.collection.server.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum CollectionSourceEnum {
    LOCAL_SCRIPT(0, "本地脚本", "local_col_"),
    THIRD_PARTY_INTERFACES_QB(1, "第三方接口-清博", "qb_col_"),
    UNKNOWN(-99, "未知", "col");;

    private final int code;
    private final String message;
    private final String esIndexPrefix;

    public static int getCode(String message){
        return Stream.of(values()).filter(b -> b.message.equals(message)).findFirst().orElse(UNKNOWN).getCode();
    }
    public static String getValue(int code){
        return Stream.of(values()).filter(b -> b.code==code).findFirst().orElse(UNKNOWN).getMessage();
    }

    public static String getEsIndexPrefix(int code){
        return Stream.of(values()).filter(b -> b.code==code).findFirst().orElse(UNKNOWN).getEsIndexPrefix();
    }
}