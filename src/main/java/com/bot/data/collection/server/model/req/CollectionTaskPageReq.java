package com.bot.data.collection.server.model.req;

import com.bot.data.collection.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@ApiModel(value="采集任务对象",description="采集任务信息")
@Data
public class CollectionTaskPageReq extends PageParam {
    /*@ApiModelProperty(value="0",name="审核人类型：0-初审人，1-复审人")
    private Integer type;*/

    @ApiModelProperty(name="任务标识")
    private String sno;

    @ApiModelProperty(name="任务名称")
    private String name;

    @ApiModelProperty(name="任务分类")
    private String ciNames;

    @ApiModelProperty(value = "项目甲-22311",name="关联项目名称/编号")
    private String project;

    @ApiModelProperty(value="0",name="采集来源（获取方式）：0-app 1-web")
    private Integer from;
    @ApiModelProperty(value="0",name="采集方式（获取来源）：0-本地脚本 1-三方接口（清博）")
    private Integer source;
    //（采集周期）采集类型 0-长期（含历史） 1-长期（指定时间开始） 2-长期（持续更新）
    @ApiModelProperty(value="0",name="采集周期（采集类型）：0-长期采集（含历史） 1-单次采集 2-时间段采集（指定时间开始）")
    private Integer cycleType;

    @ApiModelProperty(value="www.1233333001",name="采集链接或app包名" )
    private String link;

    @ApiModelProperty(value="0",name="执行状态：0-11 1-11")
    private Integer status;

    @ApiModelProperty(value = "2022-07-01 00:00:00", name = "采集开始时间", required = true)
    //@JsonFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startTime;

    @ApiModelProperty(value = "2022-07-01 00:00:00", name = "采集结束时间", required = true)
    //@JsonFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endTime;
}
