package com.bot.data.collection.server.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@ApiModel(value="任务分页查询对象",description="任务分页查询信息")
@Data
public class TaskPageReqDTO {

    @ApiModelProperty(name="用户sid")
    private String userSid;

    @ApiModelProperty(name="审核类型")
    private Integer type;

    @ApiModelProperty(name="任务标识")
    private String sno;

    @ApiModelProperty(name="任务名称")
    private String name;

    @ApiModelProperty(name="任务分类")
    private String ciNames;

    @ApiModelProperty(value = "项目甲-22311",name="关联项目名称/编号")
    private String project;

    @ApiModelProperty(value="0",name="任务状态：0 -未开始 1 -进行中")
    private Integer taskStatus;

    @ApiModelProperty(value = "2022-07-01 00:00:00", name = "初始新增时间（开始）")
    private LocalDateTime initialAddTimeStart;
    @ApiModelProperty(value = "2022-07-01 00:00:00", name = "初始新增时间（结束）")
    private LocalDateTime initialAddTimeEnd;

    @ApiModelProperty(value = "2022-07-01 00:00:00", name = "最新新增时间（开始）")
    private LocalDateTime newestAddTimeStart;
    @ApiModelProperty(value = "2022-07-01 00:00:00", name = "最新新增时间（开始）")
    private LocalDateTime newestAddTimeEnd;

}
