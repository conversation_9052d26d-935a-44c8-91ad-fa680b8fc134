package com.bot.data.collection.server.model.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bot.data.collection.common.pojo.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 分类信息：主分类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("classify_info")
public class ClassifyInfoDO extends BaseDO implements Serializable {
    //sid
    @TableId(value = "sid")
    private String sid;
    //分类代码
    private String code;
    //分类名称
    private String name;
    //分类父级sid
    private String parentSid;
    //分类代码路径
    private String codePath;
    //创建时间
    /*@TableField(value = "ci_create_time")
    private LocalDateTime ciCreateTime;
    //更新时间
    @TableField(value = "ci_update_time", insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private LocalDateTime ciUpdateTime;
    //操作人sid
    @TableField(value = "ci_operator_sid")
    private String ciOperatorSid;*/


}