package com.bot.data.collection.server.model.singleton;

import com.bot.data.collection.common.utils.DateUtils;
import com.bot.data.collection.server.constants.EsFieldConstant;
import com.bot.data.collection.server.constants.EsTypeEnum;
import com.bot.data.collection.server.model.dto.CollectionFlatDTO;
import com.bot.data.collection.server.model.es.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ES映射单例枚举
 */
public enum EsMapperSingletonEnum {
    INSTANCE;
    private EsMapperDTO esMapperDTO;
    private List<CollectionFlatDTO> collectionFlatDTOList;

    EsMapperSingletonEnum() {
        this.esMapperDTO = createEsMapperDTO();
        this.collectionFlatDTOList = createCollectionFlatDTOList();
    }

    private List<CollectionFlatDTO> createCollectionFlatDTOList() {
        List<CollectionFlatDTO> list = new ArrayList<>();
        CollectionFlatDTO dto = new CollectionFlatDTO();
        dto.setEname(EsFieldConstant.AUDIT_STATUS);
        dto.setCname(EsFieldConstant.AUDIT_STATUS_NAME);
        dto.setType(EsTypeEnum.KEYWORD.getCode());
        list.add(dto);
        dto = new CollectionFlatDTO();
        dto.setEname(EsFieldConstant.FIRST_AUDITOR);
        dto.setCname(EsFieldConstant.FIRST_AUDITOR_NAME);
        dto.setType(EsTypeEnum.KEYWORD.getCode());
        list.add(dto);
        dto = new CollectionFlatDTO();
        dto.setEname(EsFieldConstant.TWO_AUDITOR);
        dto.setCname(EsFieldConstant.TWO_AUDITOR_NAME);
        dto.setType(EsTypeEnum.KEYWORD.getCode());
        list.add(dto);
        dto = new CollectionFlatDTO();
        dto.setEname(EsFieldConstant.CREATE_TIME);
        dto.setCname(EsFieldConstant.CREATE_TIME_NAME);
        dto.setType(EsTypeEnum.DATE_DEFAULT.getCode());
        list.add(dto);
        dto = new CollectionFlatDTO();
        dto.setEname(EsFieldConstant.RELEASE_TIME);
        dto.setCname(EsFieldConstant.RELEASE_TIME_NAME);
        dto.setType(EsTypeEnum.DATE_DEFAULT.getCode());
        list.add(dto);
        return list;
    }

    public List<CollectionFlatDTO> getCollectionFlatDTOList() {
        return this.collectionFlatDTOList;
    }

    public EsMapperDTO getEsMapperDTO() {
        return this.esMapperDTO;
    }

    public Map<String, EsFormatDTO> getEsProperties() {
        return this.esMapperDTO.getEsMappingsDTO().getProperties();
    }

    public EsMapperDTO createEsMapperDTO() {
        EsMapperDTO esMapperDTO = new EsMapperDTO();
        EsIndexDTO esIndexDTO = new EsIndexDTO();
        esIndexDTO.setMaxResultWindow("1000000000");
        EsSettingsDTO esSettingsDTO = new EsSettingsDTO();
        esSettingsDTO.setNumberOfShards(6);
        esSettingsDTO.setNumberOfReplicas(1);
        esSettingsDTO.setIndex(esIndexDTO);

        EsMappingsDTO esMappingsDTO = new EsMappingsDTO();
        /*EsFormatDTO esFormatDTO = new EsFormatDTO();
        esFormatDTO.setType("keyword");
        Map<String, EsFormatDTO> properties = new HashMap<>();
        properties.put("taskId", esFormatDTO);
        properties.put("collectionId", esFormatDTO.clone());
        properties.put("classify", esFormatDTO.clone());
        properties.put("library", esFormatDTO.clone());*/
        //properties.put("ext", esFormatDTO.clone());

        EsFormatDTO esFormatDTO = new EsFormatDTO();
        esFormatDTO.setType("date");
        esFormatDTO.setFormat(DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND);
        Map<String, EsFormatDTO> properties = new HashMap<>();
        properties.put(EsFieldConstant.RELEASE_TIME, esFormatDTO);  //发布时间
        properties.put(EsFieldConstant.CREATE_TIME, esFormatDTO.clone()); //创建时间
        properties.put(EsFieldConstant.UPDATE_TIME, esFormatDTO.clone()); //创建时间

        EsFormatDTO esFormatDTO1 = new EsFormatDTO();
        esFormatDTO1.setType("keyword");
        properties.put(EsFieldConstant.AUDIT_STATUS, esFormatDTO1);
        properties.put(EsFieldConstant.FIRST_AUDITOR, esFormatDTO1.clone());
        properties.put(EsFieldConstant.TWO_AUDITOR, esFormatDTO1.clone());
        esMappingsDTO.setProperties(properties);

        esMapperDTO.setEsSettingsDTO(esSettingsDTO);
        esMapperDTO.setEsMappingsDTO(esMappingsDTO);
        return esMapperDTO;
    }
}
