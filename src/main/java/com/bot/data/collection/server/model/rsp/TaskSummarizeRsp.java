package com.bot.data.collection.server.model.rsp;

import com.bot.data.collection.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
@ApiModel(value="任务汇总对象",description="任务汇总对象")
@Data
public class TaskSummarizeRsp {

    @ApiModelProperty(value="11111",name="任务sid",required=true)
    private String sid;
    @ApiModelProperty(value="11111",name="任务标识",required=true)
    private String sno;
    @ApiModelProperty(value="11111",name="任务名称")
    private String name;
    @ApiModelProperty(name="关联项目名称/编号")
    private String project;
    @ApiModelProperty(name="任务分类")
    private String ciNames;

    @ApiModelProperty(name="任务状态")
    private Integer taskStatus;
    @ApiModelProperty(name="任务状态")
    private String taskStatusValue;
    @ApiModelProperty(value="2022-07-01 00:00:00",name="任务开始时间")
    @JsonFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime initialAddTime;
    @ApiModelProperty(value="2022-07-01 00:00:00",name="最后新增时间")
    @JsonFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime newestAddTime;
    @ApiModelProperty(value="111",name="今日新增数据量")
    private Long todayAddCount;
    @ApiModelProperty(value="111",name="总数据量")
    private Long total;
    @ApiModelProperty(value="111",name="入库数据量")
    private Long enterCount;
    @ApiModelProperty(value="111",name="导出数据量")
    private Long exportCount;
    @ApiModelProperty(value="zhangsan",name="创建人名称")
    private String updaterName;

    @ApiModelProperty(value="001",name="（任务分类）数据分类编号路径")
    private String ciCodePath;
    @ApiModelProperty(value="0",name="采集方式（获取来源）：0-本地脚本 1-三方接口（清博）")
    private Integer source;
}
