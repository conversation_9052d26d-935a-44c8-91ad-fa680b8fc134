package com.bot.data.collection.server.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bot.data.collection.common.exception.ServiceException;
import com.bot.data.collection.common.pojo.PageResult;
import com.bot.data.collection.common.utils.DateUtils;
import com.bot.data.collection.common.utils.UUIDUtils;
import com.bot.data.collection.server.constants.AuditorTypeEnum;
import com.bot.data.collection.server.constants.EsTypeEnum;
import com.bot.data.collection.server.constants.PiTypeNoEnum;
import com.bot.data.collection.server.constants.ServerErrorEnum;
import com.bot.data.collection.server.convert.ProjectInfoConvertor;
import com.bot.data.collection.server.mapper.CollectionFlatTemplateMapper;
import com.bot.data.collection.server.mapper.CollectionTaskMapper;
import com.bot.data.collection.server.mapper.ProjectInfoAuditorMapper;
import com.bot.data.collection.server.mapper.ProjectInfoMapper;
import com.bot.data.collection.server.model.dto.CollectionFlatDTO;
import com.bot.data.collection.server.model.entity.CollectionFlatTemplateDO;
import com.bot.data.collection.server.model.entity.ProjectInfoAuditorDO;
import com.bot.data.collection.server.model.entity.ProjectInfoDO;
import com.bot.data.collection.server.model.es.EsFormatDTO;
import com.bot.data.collection.server.model.es.EsMapperDTO;
import com.bot.data.collection.server.model.req.*;
import com.bot.data.collection.server.model.rsp.FlatTemplateRsp;
import com.bot.data.collection.server.model.rsp.ProjectInfoRsp;
import com.bot.data.collection.server.model.singleton.EsMapperSingletonEnum;
import com.bot.data.collection.server.service.ProjectInfoService;
import com.bot.data.collection.system.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ProjectInfoServiceImpl implements ProjectInfoService {

    @Resource
    ProjectInfoMapper projectInfoMapper;
    @Resource
    ProjectInfoAuditorMapper projectInfoAuditorMapper;
    @Resource
    CollectionFlatTemplateMapper collectionFlatTemplateMapper;
    @Resource
    CollectionTaskMapper collectionTaskMapper;
    @Override
    public PageResult<ProjectInfoRsp> getProjectInfoPage(ProjectPageReq reqVO) {
        PageResult<ProjectInfoDO> projectInfoPage = projectInfoMapper.selectPage(reqVO);
        return ProjectInfoConvertor.INSTANCE.convert(projectInfoPage);
    }

    @Override
    public ProjectInfoRsp getProjectInfo(String sid) {
        ProjectInfoDO projectInfoDO = projectInfoMapper.selectById(sid);
        if (projectInfoDO == null) {
            throw new ServiceException(ServerErrorEnum.PROJECT_INFO_EXISTS);
        }
        ProjectInfoRsp resp = ProjectInfoConvertor.INSTANCE.convert(projectInfoDO);

        //放入初审人、复审人信息
        List<ProjectInfoAuditorDO> list = projectInfoAuditorMapper.selectListByProjectSid(sid);
        //复用代码
        List<String> firstAuditorUserSids = null;
        List<String> firstAuditorUserGroupSids = null;
        List<String> twoAuditorUserSids = null;
        List<String> twoAuditorUserGroupSids = null;

        if (CollUtil.isNotEmpty(list)) {
            for (ProjectInfoAuditorDO obj : list) {
                if (AuditorTypeEnum.FIRST_AUDITOR.getCode() == obj.getType()) {
                    if (StringUtils.isNotEmpty(obj.getUserSid())){
                        if (firstAuditorUserSids == null) {
                            firstAuditorUserSids = new ArrayList<>();
                        }
                        firstAuditorUserSids.add(obj.getUserSid());
                    } else if (StringUtils.isNotEmpty(obj.getUserGroupSid())) {
                        if (firstAuditorUserGroupSids == null) {
                            firstAuditorUserGroupSids = new ArrayList<>();
                        }
                        firstAuditorUserGroupSids.add(obj.getUserGroupSid());
                    }
                } else if (AuditorTypeEnum.TWO_AUDITOR.getCode() == obj.getType()) {
                    if (StringUtils.isNotEmpty(obj.getUserSid())){
                        if (twoAuditorUserSids == null) {
                            twoAuditorUserSids = new ArrayList<>();
                        }
                        twoAuditorUserSids.add(obj.getUserSid());
                    } else if (StringUtils.isNotEmpty(obj.getUserGroupSid())) {
                        if (twoAuditorUserGroupSids == null) {
                            twoAuditorUserGroupSids = new ArrayList<>();
                        }
                        twoAuditorUserGroupSids.add(obj.getUserGroupSid());
                    }
                }
            }
            resp.setFirstAuditorUserSids(firstAuditorUserSids);
            resp.setFirstAuditorUserGroupSids(firstAuditorUserGroupSids);
            resp.setTwoAuditorUserSids(twoAuditorUserSids);
            resp.setTwoAuditorUserGroupSids(twoAuditorUserGroupSids);
        }
        return resp;
    }

    @Transactional
    @Override
    public String createProjectInfo(ProjectCreateReq reqVO) {
        if (reqVO.getPeopleAudit()) {
            if (CollUtil.isEmpty(reqVO.getFirstAuditorUserSids()) && CollUtil.isEmpty(reqVO.getFirstAuditorUserGroupSids())) {
                throw new ServiceException(ServerErrorEnum.PROJECT_FIRST_AUDITOR_NOT_EXISTS);
            }

            if (CollUtil.isEmpty(reqVO.getTwoAuditorUserSids()) && CollUtil.isEmpty(reqVO.getTwoAuditorUserGroupSids())) {
                throw new ServiceException(ServerErrorEnum.PROJECT_TWO_AUDITOR_NOT_EXISTS);
            }
        }

        ProjectInfoDO projectInfo = ProjectInfoConvertor.INSTANCE.convert(reqVO);
        String sid = UUIDUtils.getUUID();
        projectInfo.setSid(sid);
        String piSno = PiTypeNoEnum.getVale(reqVO.getPiType()) + "-" + UUIDUtils.getSno();
        projectInfo.setPiSno(piSno);
        //查询出基础模板，项目创建后默认使用基础模板
        CollectionFlatTemplateDO cftDO = collectionFlatTemplateMapper.selectById("1");
        projectInfo.setPiMapper(jointPiMapper(JSON.parseArray(cftDO.getCftFlat(), CollectionFlatDTO.class)));
        projectInfo.setPiMapperDict(cftDO.getCftFlat());
        projectInfo.setCreatorName(SecurityUtils.getLoginUser().getName());
        projectInfoMapper.insert(projectInfo);

        List<ProjectInfoAuditorDO> list = new ArrayList<>();
        //人审时，新增关系
        if (reqVO.getPeopleAudit()) {
            if (CollUtil.isNotEmpty(reqVO.getFirstAuditorUserSids())) {
                reqVO.getFirstAuditorUserSids().stream().forEach(userSid -> list.add(ProjectInfoAuditorDO.builder().sid(UUIDUtils.getUUID()).projectInfoSid(sid).userSid(userSid).type(AuditorTypeEnum.FIRST_AUDITOR.getCode()).build()));
            }
            if (CollUtil.isNotEmpty(reqVO.getFirstAuditorUserGroupSids())) {
                reqVO.getFirstAuditorUserGroupSids().stream().forEach(userGroupSid -> list.add(ProjectInfoAuditorDO.builder().sid(UUIDUtils.getUUID()).projectInfoSid(sid).userGroupSid(userGroupSid).type(AuditorTypeEnum.FIRST_AUDITOR.getCode()).build()));
            }
            if (CollUtil.isNotEmpty(reqVO.getTwoAuditorUserSids())) {
                reqVO.getTwoAuditorUserSids().stream().forEach(userSid -> list.add(ProjectInfoAuditorDO.builder().sid(UUIDUtils.getUUID()).projectInfoSid(sid).userSid(userSid).type(AuditorTypeEnum.TWO_AUDITOR.getCode()).build()));
            }
            if (CollUtil.isNotEmpty(reqVO.getTwoAuditorUserGroupSids())) {
                reqVO.getTwoAuditorUserGroupSids().stream().forEach(userGroupSid -> list.add(ProjectInfoAuditorDO.builder().sid(UUIDUtils.getUUID()).projectInfoSid(sid).userGroupSid(userGroupSid).type(AuditorTypeEnum.TWO_AUDITOR.getCode()).build()));
            }
            projectInfoAuditorMapper.insertBatch(list);
        }
        return projectInfo.getSid();
    }

    @Transactional
    @Override
    public void updateProjectInfo(ProjectUpdateReq reqVO) {
        String sid = reqVO.getSid();
        ProjectInfoDO projectInfoDO = projectInfoMapper.selectById(sid);
        if (projectInfoDO == null) {
            throw new ServiceException(ServerErrorEnum.PROJECT_INFO_NOT_EXISTS);
        }
        //校验任务
        validateProjectTaskExists(sid);

        if (!projectInfoDO.getPiType().equals(reqVO.getPiType())) {
            String piSno = PiTypeNoEnum.getVale(reqVO.getPiType()) + projectInfoDO.getPiSno().substring(projectInfoDO.getPiSno().indexOf("-"), projectInfoDO.getPiSno().length());
            projectInfoDO.setPiSno(piSno);
        }
        projectInfoDO.setPiType(reqVO.getPiType());
        projectInfoDO.setPiName(reqVO.getPiName());
        projectInfoDO.setPiDesc(reqVO.getPiDesc());
        projectInfoDO.setPeopleAudit(reqVO.getPeopleAudit());
        projectInfoMapper.updateById(projectInfoDO);

        if (!projectInfoDO.getPeopleAudit()){
            //原来没有
            if (reqVO.getPeopleAudit()){
                //现在有了，新增全部
                List<ProjectInfoAuditorDO> list = new ArrayList<>();
                if (CollUtil.isNotEmpty(reqVO.getFirstAuditorUserSids())) {
                    reqVO.getFirstAuditorUserSids().stream().forEach(userSid -> list.add(ProjectInfoAuditorDO.builder().sid(UUIDUtils.getUUID()).projectInfoSid(sid).userSid(userSid).type(AuditorTypeEnum.FIRST_AUDITOR.getCode()).build()));
                }
                if (CollUtil.isNotEmpty(reqVO.getFirstAuditorUserGroupSids())) {
                    reqVO.getFirstAuditorUserGroupSids().stream().forEach(userGroupSid -> list.add(ProjectInfoAuditorDO.builder().sid(UUIDUtils.getUUID()).projectInfoSid(sid).userGroupSid(userGroupSid).type(AuditorTypeEnum.FIRST_AUDITOR.getCode()).build()));
                }
                if (CollUtil.isNotEmpty(reqVO.getTwoAuditorUserSids())) {
                    reqVO.getTwoAuditorUserSids().stream().forEach(userSid -> list.add(ProjectInfoAuditorDO.builder().sid(UUIDUtils.getUUID()).projectInfoSid(sid).userSid(userSid).type(AuditorTypeEnum.TWO_AUDITOR.getCode()).build()));
                }
                if (CollUtil.isNotEmpty(reqVO.getTwoAuditorUserGroupSids())) {
                    reqVO.getTwoAuditorUserGroupSids().stream().forEach(userGroupSid -> list.add(ProjectInfoAuditorDO.builder().sid(UUIDUtils.getUUID()).projectInfoSid(sid).userGroupSid(userGroupSid).type(AuditorTypeEnum.TWO_AUDITOR.getCode()).build()));
                }
                projectInfoAuditorMapper.insertBatch(list);
            }
        }else {
            //原来有
            if (!reqVO.getPeopleAudit()){
                //现在没有了,—删除全部审核关系
                projectInfoAuditorMapper.deleteInfoByProjectSid(sid);
            }else {
                String loginUserSid = SecurityUtils.getLoginUserId();
                List<String> firstAuditorUserSids = null;
                List<String> firstAuditorUserGroupSids = null;
                List<String> twoAuditorUserSids = null;
                List<String> twoAuditorUserGroupSids = null;
                List<ProjectInfoAuditorDO> list = projectInfoAuditorMapper.selectListByProjectSid(sid);
                if (CollUtil.isNotEmpty(list)) {
                    for (ProjectInfoAuditorDO obj : list) {
                        if (AuditorTypeEnum.FIRST_AUDITOR.getCode() == obj.getType()) {
                            if (StringUtils.isNotEmpty(obj.getUserSid())) {
                                if (firstAuditorUserSids == null) {
                                    firstAuditorUserSids = new ArrayList<>();
                                }
                                firstAuditorUserSids.add(obj.getUserSid());
                            } else if (StringUtils.isNotEmpty(obj.getUserGroupSid())) {
                                if (firstAuditorUserGroupSids == null) {
                                    firstAuditorUserGroupSids = new ArrayList<>();
                                }
                                firstAuditorUserGroupSids.add(obj.getUserGroupSid());
                            }
                        } else if (AuditorTypeEnum.TWO_AUDITOR.getCode() == obj.getType()) {
                            if (StringUtils.isNotEmpty(obj.getUserSid())) {
                                if (twoAuditorUserSids == null) {
                                    twoAuditorUserSids = new ArrayList<>();
                                }
                                twoAuditorUserSids.add(obj.getUserSid());
                            } else if (StringUtils.isNotEmpty(obj.getUserGroupSid())) {
                                if (twoAuditorUserGroupSids == null) {
                                    twoAuditorUserGroupSids = new ArrayList<>();
                                }
                                twoAuditorUserGroupSids.add(obj.getUserGroupSid());
                            }
                        }
                    }
                }
                List<ProjectInfoAuditorDO> addDOlist = new ArrayList<>();
                //List<String> removeSidlist = new ArrayList<>();
                updateProjectInfoAuditor(1, reqVO.getFirstAuditorUserSids(), firstAuditorUserSids, sid, loginUserSid, addDOlist);
                updateProjectInfoAuditor(2, reqVO.getFirstAuditorUserGroupSids(), firstAuditorUserGroupSids, sid, loginUserSid, addDOlist);
                updateProjectInfoAuditor(3, reqVO.getTwoAuditorUserSids(), twoAuditorUserSids, sid, loginUserSid, addDOlist);
                updateProjectInfoAuditor(4, reqVO.getTwoAuditorUserGroupSids(), twoAuditorUserGroupSids, sid, loginUserSid, addDOlist);
                if (CollUtil.isNotEmpty(addDOlist)){
                    projectInfoAuditorMapper.insertBatch(addDOlist);
                }
            }
        }


    }

    @Transactional
    @Override
    public void deleteProjectInfo(String sid) {
        ProjectInfoDO projectInfoDO = projectInfoMapper.selectById(sid);
        if (projectInfoDO == null) {
            throw new ServiceException(ServerErrorEnum.DATA_INFO_NOT_EXISTS);
        }
        //校验任务
        validateProjectTaskExists(sid);
        projectInfoMapper.deleteById(sid);

        //人审时，删除审核关系信息
        if (projectInfoDO.getPeopleAudit()) {
            projectInfoAuditorMapper.deleteInfoByProjectSid(sid);
        }
        //同时删除所有关联任务（已停止的）
        collectionTaskMapper.deleteByPiSid(sid);
    }

    @Override
    public List<FlatTemplateRsp> getCollectionFlatTemplateList() {
        List<CollectionFlatTemplateDO> collectionFlatTemplateList = collectionFlatTemplateMapper.selectList();
        if (CollectionUtils.isNotEmpty(collectionFlatTemplateList)){
            List<FlatTemplateRsp> list = new ArrayList<>();
            collectionFlatTemplateList.stream().forEach(o -> {
                FlatTemplateRsp flatTemplateRsp = new FlatTemplateRsp();
                flatTemplateRsp.setSid(o.getSid());
                flatTemplateRsp.setCftName(o.getCftName());
                flatTemplateRsp.setCftFlat(JSON.parseArray(o.getCftFlat(), CollectionFlatDTO.class));
                list.add(flatTemplateRsp);
            });
            return list;
        }
        return null;
    }

    @Override
    public String createCollectionFlatTemplate(CollectionFlatTemplateCreateReq reqVO) {
        //校验名称是否重复
        validateCollectionFlatTemplateNameUnique(null, reqVO.getCftName());
        List<CollectionFlatDTO> newCftFlat = reqVO.getCftFlat();
        //校验：个数不能为空，EName不能重复、type不能为空
        List<String> ENames = new ArrayList<>();
        for (CollectionFlatDTO collectionFlatDTO : newCftFlat) {
            if (null == collectionFlatDTO.getEname()){
                throw new ServiceException(ServerErrorEnum.FIELD_ENNAME_NOT_NULL);
            }
            if (null == collectionFlatDTO.getType()){
                throw new ServiceException(ServerErrorEnum.FIELD_TYPE_NOT_NULL);
            }
            if (ENames.contains(collectionFlatDTO.getEname())){
                throw new ServiceException(ServerErrorEnum.FIELD_ENNAME_NOT_REPEAT);
            }
            ENames.add(collectionFlatDTO.getEname());
        }

        //查询出已有模板
        //新增
        CollectionFlatTemplateDO collectionFlatTemplate = new CollectionFlatTemplateDO();
        collectionFlatTemplate.setSid(UUIDUtils.getUUID());
        collectionFlatTemplate.setCftType(1);
        collectionFlatTemplate.setCftName(reqVO.getCftName());
        collectionFlatTemplate.setCftFlat(JSON.toJSONString(reqVO.getCftFlat()));
        collectionFlatTemplateMapper.insert(collectionFlatTemplate);
        return collectionFlatTemplate.getSid();
    }

    @Override
    public void deleteCollectionFlatTemplate(String sid) {
        CollectionFlatTemplateDO cftDO = collectionFlatTemplateMapper.selectById(sid);
        if (cftDO == null) {
            throw new ServiceException(ServerErrorEnum.DATA_INFO_NOT_EXISTS);
        }
        collectionFlatTemplateMapper.deleteById(sid);
    }

    @Override
    public void updateProjectInfoMapper(ProjectMapperUpdateReq reqVO) {
        String sid = reqVO.getSid();
        ProjectInfoDO projectInfo = projectInfoMapper.selectById(sid);
        if (projectInfo == null) {
            throw new ServiceException(ServerErrorEnum.DATA_INFO_NOT_EXISTS);
        }
        //赋值：非清博从单例对象获取结构
        projectInfo.setPiMapper(jointPiMapper(reqVO.getFlat()));
        projectInfo.setPiMapperDict(JSON.toJSONString(reqVO.getFlat()));
        projectInfoMapper.updateById(projectInfo);
    }

    @Override
    public List<ProjectSketchRsp> getProjectSketchList() {
        List<ProjectInfoDO> projectInfoList = projectInfoMapper.selectList();
        if (CollectionUtils.isNotEmpty(projectInfoList)){
            List<ProjectSketchRsp> list = new ArrayList<>();
            projectInfoList.stream().forEach(o -> {
                ProjectSketchRsp projectSketchRsp = new ProjectSketchRsp();
                projectSketchRsp.setSid(o.getSid());
                projectSketchRsp.setPiSno(o.getPiSno());
                projectSketchRsp.setCtProject(o.getPiName() + "-" + o.getPiSno());
                list.add(projectSketchRsp);
            });
            return list;
        }
        return null;
    }

    @Override
    public List<CollectionFlatDTO> getCollectDataMappingDict(String sid) {
        ProjectInfoDO projectInfoDO = projectInfoMapper.selectById(sid);
        if (projectInfoDO == null) {
            throw new ServiceException(ServerErrorEnum.PROJECT_INFO_NOT_EXISTS);
        }
        List<CollectionFlatDTO> list = null;
        String piMapperDict= projectInfoDO.getPiMapperDict();
        if (StringUtils.isNotEmpty(piMapperDict)){
            list = JSONObject.parseArray(piMapperDict, CollectionFlatDTO.class);
            list.addAll(EsMapperSingletonEnum.INSTANCE.getCollectionFlatDTOList());
        }
        return list;
    }

    private String jointPiMapper(List<CollectionFlatDTO> flatDTOS) {
        EsMapperDTO esMapperDTO = EsMapperSingletonEnum.INSTANCE.createEsMapperDTO();
        Map<String, EsFormatDTO> properties = esMapperDTO.getEsMappingsDTO().getProperties();
        for (CollectionFlatDTO collectionFlatDTO : flatDTOS){
            EsFormatDTO esFormatDTO = new EsFormatDTO();
            Integer type = collectionFlatDTO.getType();
            //类型枚举映射
            esFormatDTO.setType(EsTypeEnum.getValue(type));
            if (EsTypeEnum.DATE_DEFAULT.getCode().equals(type)) {
                esFormatDTO.setFormat(DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND);
            }
            properties.put(collectionFlatDTO.getEname(), esFormatDTO);

            /*CollectionFlatDTO flatDTO = new CollectionFlatDTO();
            flatDTO.setEName(collectionFlatDTO.getEName());
            flatDTO.setCName(collectionFlatDTO.getCName());
            flatDTO.setType(collectionFlatDTO.getType());
            if (EsTypeEnum.OBJECT.getCode().equals(type)) {
                flatDTO.setFlat(collectionFlatDTO.getFlat());
            }
            flatDTOS.add(flatDTO);*/
            //dict.put(collectionFlatDTO.getEName(), collectionFlatDTO.getCName());
        }
        esMapperDTO.getEsMappingsDTO().setProperties(properties);
        return JSON.toJSONString(esMapperDTO);
    }

    /**
     * 更新项目审核人
     * @param numFlag 数字标识
     * @param newSids
     * @param oldSids
     * @param projectInfoSid
     * @param loginUserSid 当前登陆人sid
     * @param addDOlist
     */
    private void updateProjectInfoAuditor(int numFlag, List<String> newSids, List<String> oldSids, String projectInfoSid, String loginUserSid, List<ProjectInfoAuditorDO> addDOlist) {
        List<String> addSids = null;
        List<String> removeSids = null;
        if (CollUtil.isEmpty(oldSids) && CollUtil.isNotEmpty(newSids)) {
            //全部新增
            addSids = newSids;
        } else if (CollUtil.isEmpty(newSids) && CollUtil.isNotEmpty(oldSids)) {
            //全部删除
            removeSids = oldSids;
        }else if (CollUtil.isNotEmpty(newSids) && CollUtil.isNotEmpty(oldSids)) {
            addSids = newSids.stream()
                    .filter(e -> !oldSids.contains(e))
                    .collect(Collectors.toList());
            removeSids = oldSids.stream()
                    .filter(e -> !newSids.contains(e))
                    .collect(Collectors.toList());
        }
        if (CollUtil.isNotEmpty(addSids)) {
            if (numFlag == 1) {
                addSids.forEach(e -> addDOlist.add(ProjectInfoAuditorDO.builder().sid(UUIDUtils.getUUID()).projectInfoSid(projectInfoSid).userSid(e).type(AuditorTypeEnum.FIRST_AUDITOR.getCode()).build()));
            } else if (numFlag == 2) {
                addSids.forEach(e -> addDOlist.add(ProjectInfoAuditorDO.builder().sid(UUIDUtils.getUUID()).projectInfoSid(projectInfoSid).userGroupSid(e).type(AuditorTypeEnum.FIRST_AUDITOR.getCode()).build()));
            } else if (numFlag == 3) {
                addSids.forEach(e -> addDOlist.add(ProjectInfoAuditorDO.builder().sid(UUIDUtils.getUUID()).projectInfoSid(projectInfoSid).userSid(e).type(AuditorTypeEnum.TWO_AUDITOR.getCode()).build()));
            } else if (numFlag == 4) {
                addSids.forEach(e -> addDOlist.add(ProjectInfoAuditorDO.builder().sid(UUIDUtils.getUUID()).projectInfoSid(projectInfoSid).userGroupSid(e).type(AuditorTypeEnum.TWO_AUDITOR.getCode()).build()));

            }
        }
        if (CollUtil.isNotEmpty(removeSids)) {
            if (numFlag == 1) {
                projectInfoAuditorMapper.deleteBatchByFirstAuditorUserSids(removeSids, projectInfoSid, loginUserSid);
            } else if (numFlag == 2) {
                projectInfoAuditorMapper.deleteBatchByFirstAuditorUserGroupSids(removeSids, projectInfoSid, loginUserSid);
            } else if (numFlag == 3) {
                projectInfoAuditorMapper.deleteBatchByTwoAuditorUserSids(removeSids, projectInfoSid, loginUserSid);
            } else if (numFlag == 4) {
                projectInfoAuditorMapper.deleteBatchByTwoAuditorUserGroupSids(removeSids, projectInfoSid, loginUserSid);
            }
        }
    }

    /**
     * 校验项目任务是否存在（存在时抛出异常）
     * @param sid
     */
    private void validateProjectTaskExists(String sid){
        if (collectionTaskMapper.selectCountByPiSid(sid) > 0) {
            throw new ServiceException(ServerErrorEnum.PROJECT_TASK_IS_EXISTS);
        }
    }

    /**
     * 校验采集模板名唯一
     * @param name
     */
    void validateCollectionFlatTemplateNameUnique(String sid, String name) {
        if (collectionFlatTemplateMapper.selectCountByCftName(sid, name) > 0) {
            throw new ServiceException(ServerErrorEnum.COLLECTION_FLAT_TEMPLATE_NAME_EXISTS);
        }
    }
}
