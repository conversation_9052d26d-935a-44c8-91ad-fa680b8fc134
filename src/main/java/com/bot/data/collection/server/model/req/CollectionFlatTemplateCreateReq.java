package com.bot.data.collection.server.model.req;

import com.bot.data.collection.server.model.dto.CollectionFlatDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@ApiModel("采集结构模板创建 Request VO")
@Data
public class CollectionFlatTemplateCreateReq{
    @NotEmpty(message = "模板名称不能为空")
    @ApiModelProperty(value="自定义",name="模板名称", required = true)
    private String cftName;
    @NotEmpty(message = "模板字段不能为空")
    @ApiModelProperty(name="模板结构", required = true)
    private List<CollectionFlatDTO> cftFlat;
}
