package com.bot.data.collection.server.model.req;

import com.bot.data.collection.server.model.dto.DataTermDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Set;

@ApiModel(value="任务数据信息分页查询条件对象",description="任务数据信息分页查询条件对象")
@Data
public class TaskDataInfoQueryReq{

    @ApiModelProperty(value="123301",name="任务sid")
    private String taskSid;

    @ApiModelProperty(value="0",name="数据查询条件")
    private Set<DataTermDTO> dataTermDTOS;

    @ApiModelProperty(value="0",name="审核状态")
    private Integer auditStatus;

    @ApiModelProperty(value = "2022-07-01 00:00:00", name = "发布时间（开始）")
    private LocalDateTime releaseTimeStart;

    @ApiModelProperty(value = "2022-07-01 00:00:00", name = "发布时间（结束）")
    private LocalDateTime releaseTimeEnd;

    @ApiModelProperty(value = "2022-07-01 00:00:00", name = "创建时间（开始）")
    private LocalDateTime createTimeStart;

    @ApiModelProperty(value = "2022-07-01 00:00:00", name = "创建时间（结束）")
    private LocalDateTime createTimeEnd;
}
