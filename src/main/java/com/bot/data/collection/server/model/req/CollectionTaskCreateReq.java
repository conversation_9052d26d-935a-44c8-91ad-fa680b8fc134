package com.bot.data.collection.server.model.req;

import com.bot.data.collection.server.model.base.CollectionTaskBaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;


@ApiModel("采集任务信息更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class CollectionTaskCreateReq extends CollectionTaskBaseVO {
    @ApiModelProperty(value="2022-07-01 00:00:00",name="采集开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty(value="2022-07-01 00:00:00",name="采集结束时间")
    private LocalDateTime endTime;

}
