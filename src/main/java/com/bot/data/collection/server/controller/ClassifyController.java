package com.bot.data.collection.server.controller;

import com.bot.data.collection.common.exception.ServiceException;
import com.bot.data.collection.common.pojo.CommonResult;
import com.bot.data.collection.server.constants.ServerErrorEnum;
import com.bot.data.collection.server.model.req.ClassifyCreateReq;
import com.bot.data.collection.server.model.req.ClassifyUpdateReq;
import com.bot.data.collection.server.model.rsp.ClassifyTreeRsp;
import com.bot.data.collection.server.service.ClassifyInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;

@Api(tags = "分类管理")
@RestController
@RequestMapping("/classify")
@Validated
public class ClassifyController {

    @Resource
    ClassifyInfoService classifyInfoService;

    @GetMapping("/tree")
    @ApiOperation(value = "查询分类树")
    public CommonResult<ClassifyTreeRsp> getClassifyTree() {
        ClassifyTreeRsp rsp = new ClassifyTreeRsp();
        rsp.setClassDTOList(classifyInfoService.getClassifyLibraryList());
        rsp.setLock(classifyInfoService.getClassifyLockAdd());
        return CommonResult.success(rsp);
    }

    @PostMapping("/create")
    @ApiOperation(value = "新增分类信息")
    //@PreAuthorize("@ss.hasPermission('system:user:create')")
    public CommonResult<String> createClassifyInfo(@Valid @RequestBody ClassifyCreateReq reqVO) {
        return CommonResult.success(classifyInfoService.createClassifyInfo(reqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新分类信息")
    //@PreAuthorize("@ss.hasPermission('system:user:update')")
    public CommonResult<Boolean> updateClassifyInfo(@Valid @RequestBody ClassifyUpdateReq reqVO) {
        classifyInfoService.updateClassifyInfo(reqVO);
        return CommonResult.success(true);
    }

    @Operation(summary = "更新分类的锁")
    @GetMapping(value = "/updateClassifyLock")
    //@PermitAll
    public CommonResult<Boolean> updateClassifyLock(@RequestParam(value = "lock") Boolean lock) {
        if (null == lock) {
            throw new ServiceException(ServerErrorEnum.PARAM_IS_NULL);
        }
        classifyInfoService.updateClassifyLock(lock);
        return CommonResult.success();
    }


    /*@Operation(summary = "导入分类数据")
    @GetMapping(value = "/import")
    @PermitAll
    public CommonResult<Boolean> importClassifyData() {
        classifyInfoService.importClassifyData();
        return CommonResult.success();
    }*/


}
