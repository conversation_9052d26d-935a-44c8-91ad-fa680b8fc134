package com.bot.data.collection.server.convert;

import com.bot.data.collection.common.pojo.PageResult;
import com.bot.data.collection.server.model.dto.TaskPageReqDTO;
import com.bot.data.collection.server.model.entity.CollectionTaskDO;
import com.bot.data.collection.server.model.req.CollectionTaskCreateReq;
import com.bot.data.collection.server.model.req.CollectionTaskUpdateReq;
import com.bot.data.collection.server.model.req.TaskDisposePageReq;
import com.bot.data.collection.server.model.req.TaskSummarizePageReq;
import com.bot.data.collection.server.model.rsp.*;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface CollectionTaskConvertor {

    CollectionTaskInfoRsp convert(CollectionTaskDO obj);

    CollectionTaskDO convert(CollectionTaskCreateReq obj);
    CollectionTaskDO convertDO(CollectionTaskUpdateReq obj);

    TaskPageReqDTO convert(TaskSummarizePageReq obj);

    TaskPageReqDTO convert(TaskDisposePageReq obj);

    PageResult<CollectionTaskRsp> convertPage(PageResult<CollectionTaskDO> obj);

    PageResult<TaskSummarizeRsp> convert1(PageResult<CollectionTaskDO> obj);

    List<TaskSummarizeRsp> convert(List<CollectionTaskDO> obj);

    List<TaskDisposeRsp> convertTaskDisposeRsp(List<CollectionTaskDO> obj);

    PageResult<TaskDisposeRsp> convertDisposePage(PageResult<CollectionTaskDO> projectInfoPage);

    DataStatRsp convertDataStatRsp(CollectionTaskDO collectionTaskDO);
}
