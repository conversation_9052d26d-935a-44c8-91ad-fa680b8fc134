package com.bot.data.collection.server.mapper;

import com.bot.data.collection.common.mybatis.mapper.BaseMapperX;
import com.bot.data.collection.common.mybatis.query.LambdaQueryWrapperX;
import com.bot.data.collection.common.pojo.PageResult;
import com.bot.data.collection.server.model.entity.ProjectInfoDO;
import com.bot.data.collection.server.model.req.ProjectPageReq;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface ProjectInfoMapper extends BaseMapperX<ProjectInfoDO> {
    default PageResult<ProjectInfoDO> selectPage(ProjectPageReq request) {
        return selectPage(request, new LambdaQueryWrapperX<ProjectInfoDO>()
                .likeIfPresent(ProjectInfoDO::getPiSno, request.getPiSno())
                .likeIfPresent(ProjectInfoDO::getPiName, request.getPiName())
                .eqIfPresent(ProjectInfoDO::getPiType, request.getPiType())
                .betweenIfPresent(ProjectInfoDO::getCreateTime, request.getCreateTime())
                .orderByDesc(ProjectInfoDO::getCreateTime));
    }


}
