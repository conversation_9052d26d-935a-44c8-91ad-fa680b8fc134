package com.bot.data.collection.server.service;

import com.bot.data.collection.common.pojo.PageResult;
import com.bot.data.collection.server.model.dto.CollectionFlatDTO;
import com.bot.data.collection.server.model.req.DataStatReq;
import com.bot.data.collection.server.model.req.TaskDataInfoPageReq;
import com.bot.data.collection.server.model.req.TaskSummarizePageReq;
import com.bot.data.collection.server.model.rsp.DataStatRsp;
import com.bot.data.collection.server.model.rsp.TaskSummarizeRsp;

import java.util.List;
import java.util.Map;

public interface DataSummarizeService {
    PageResult<TaskSummarizeRsp> getTaskSummarizePage(TaskSummarizePageReq reqVO);

    PageResult<Map<String, Object>> getCollectDataInfoPage(TaskDataInfoPageReq reqVO);

    List<CollectionFlatDTO> getCollectDataMappingDict(String taskSid);


    DataStatRsp getDataStat(DataStatReq reqVO);
}
