package com.bot.data.collection.server.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum PiTypeNoEnum {
    TI(0,"TI"), //内部项目
    TB(1,"TB"), //业务相关
    TS(2,"TS"), //对外项目
    UNKNOWN(-99,"");

    private final Integer code;

    private final String value;


    public static String getVale(Integer code){
        return Stream.of(values()).filter(b -> b.code.equals(code)).findFirst().orElse(UNKNOWN).getValue();
    }
}
