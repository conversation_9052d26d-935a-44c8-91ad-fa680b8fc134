package com.bot.data.collection.server.model.req;

import com.bot.data.collection.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@ApiModel(value="采集任务对象",description="采集任务信息")
@Data
public class TaskDisposePageReq extends PageParam {
    @ApiModelProperty(name="任务标识")
    private String sno;

    @ApiModelProperty(name="任务名称")
    private String name;

    @ApiModelProperty(name="任务分类")
    private String ciNames;

    @ApiModelProperty(value = "项目甲-22311",name="关联项目名称/编号")
    private String project;


}
