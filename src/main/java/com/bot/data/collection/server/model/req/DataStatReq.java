package com.bot.data.collection.server.model.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

@ApiModel(value="采集新增数据量统计查询条件对象",description="采集新增数据量统计查询条件对象")
@Data
public class DataStatReq {

    @ApiModelProperty(value="123301",name="任务sid")
    private String taskSid;

    @ApiModelProperty(value = "2022-07-01", name = "创建时间（开始）")
    private LocalDate createTimeStart;

    @ApiModelProperty(value = "2022-07-01", name = "创建时间（结束）")
    private LocalDate createTimeEnd;


}
