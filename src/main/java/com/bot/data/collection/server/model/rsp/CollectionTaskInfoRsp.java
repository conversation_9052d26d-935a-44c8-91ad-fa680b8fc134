package com.bot.data.collection.server.model.rsp;

import com.bot.data.collection.server.model.base.CollectionTaskBaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@ApiModel(value="采集任务对象",description="采集任务信息")
@Data
public class CollectionTaskInfoRsp extends CollectionTaskBaseVO {
    @ApiModelProperty(value="11111",name="项目sid",required=true)
    private String sid;
    @ApiModelProperty(value="11111",name="任务标识",required=true)
    private String sno;
    @ApiModelProperty(value="2022-07-01 00:00:00",name="采集开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty(value="2022-07-01 00:00:00",name="采集结束时间")
    private LocalDateTime endTime;
}
