package com.bot.data.collection.server.service;

import com.bot.data.collection.common.pojo.PageResult;
import com.bot.data.collection.server.model.req.CollectionTaskCreateReq;
import com.bot.data.collection.server.model.req.CollectionTaskPageReq;
import com.bot.data.collection.server.model.req.CollectionTaskUpdateReq;
import com.bot.data.collection.server.model.rsp.CollectionTaskInfoRsp;
import com.bot.data.collection.server.model.rsp.CollectionTaskRsp;

public interface CollectionTaskService {
    PageResult<CollectionTaskRsp> getCollectionTaskPage(CollectionTaskPageReq reqVO);

    CollectionTaskInfoRsp getCollectionTaskInfo(String sid);

    String createCollectionTaskInfo(CollectionTaskCreateReq reqVO);

    void updateCollectionTaskInfo(CollectionTaskUpdateReq reqVO);

    void deleteCollectionTaskInfo(String sid);
}
