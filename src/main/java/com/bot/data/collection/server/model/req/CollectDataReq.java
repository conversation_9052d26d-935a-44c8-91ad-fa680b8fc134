package com.bot.data.collection.server.model.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.Set;

@ApiModel(value="采集数据对象",description="采集数据对象")
@Data
public class CollectDataReq {

    @ApiModelProperty(value="0",name="任务id")
    @NotEmpty(message = "任务id不能为空")
    private String taskSid;

    @ApiModelProperty(value="0",name="数据id集合")
    @NotEmpty(message = "数据id集合不能为空")
    private Set<String> dataIds;


}
