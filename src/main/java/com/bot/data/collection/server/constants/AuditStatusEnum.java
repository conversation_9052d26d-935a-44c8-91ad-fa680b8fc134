package com.bot.data.collection.server.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

/**
 * 审核状态枚举类
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum AuditStatusEnum {

    AWAIT_DISPATCH(0, "待派发"), // 初审人
    ALREADY_DISPATCH(1, "已派发"), // 待初审提交
    AWAIT_TWO_AUDIT(2, "待复审"), // 已初审提交
    ALREADY_AUDITOR_REJECT(3, "已打回"), // 待初审提交
    ALREADY_AUDIT_PASS(4, "已通过"), // 已复审提交通过

    UNKNOWN(-99, "未知");

    private final int code;
    private final String value;

    public static int getCode(String value){
        return Stream.of(values()).filter(b -> b.value.equals(value)).findFirst().orElse(UNKNOWN).getCode();
    }
    public static String getValue(int code){
        return Stream.of(values()).filter(b -> b.code==code).findFirst().orElse(UNKNOWN).getValue();
    }

}
