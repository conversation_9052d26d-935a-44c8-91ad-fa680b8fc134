package com.bot.data.collection.server.model.base;

import com.bot.data.collection.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
@Data
public class BaseVO {
    @ApiModelProperty(value="zhangsan",name="创建人")
    private String creator;

    @ApiModelProperty(value = "2022-07-01 00:00:00", name = "创建时间", required = true)
    @JsonFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime piCreateTime;

    @ApiModelProperty(value = "2022-07-01 00:00:00", name = "更新时间", required = true)
    @JsonFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime updateTime;
}
