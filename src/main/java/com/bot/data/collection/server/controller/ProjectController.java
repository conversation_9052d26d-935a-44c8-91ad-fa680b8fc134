package com.bot.data.collection.server.controller;

import com.bot.data.collection.common.exception.ServiceException;
import com.bot.data.collection.common.pojo.CommonResult;
import com.bot.data.collection.common.pojo.PageResult;
import com.bot.data.collection.server.model.req.*;
import com.bot.data.collection.server.model.rsp.FlatTemplateRsp;
import com.bot.data.collection.server.model.rsp.ProjectInfoRsp;
import com.bot.data.collection.server.service.ProjectInfoService;
import com.bot.data.collection.system.constants.SystemErrorEnum;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;
import java.util.List;

@Api(tags = "项目管理")
@RestController
@RequestMapping("/project")
@Validated
public class ProjectController {

    @Resource
    ProjectInfoService projectInfoService;


    @PostMapping("/page")
    @ApiOperation(value = "获得项目分页列表")
    //@PermitAll
    //@PreAuthorize("@ss.hasPermission('system:user:list')")
    public CommonResult<PageResult<ProjectInfoRsp>> getProjectPage(@Valid @RequestBody ProjectPageReq reqVO) {
        // 获得用户分页列表
        return CommonResult.success(projectInfoService.getProjectInfoPage(reqVO));
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得项目信息详情")
    //@PermitAll
    //@PreAuthorize("@ss.hasPermission('system:user:query')")
    public CommonResult<ProjectInfoRsp> getProjectInfo(@ApiParam(name = "sid", value = "123", required = true) @RequestParam("sid") String sid) {
        if (StringUtils.isBlank(sid)) {
            throw new ServiceException(SystemErrorEnum.PARAM_IS_NULL);
        }
        return CommonResult.success(projectInfoService.getProjectInfo(sid));
    }

    @PostMapping("/create")
    @ApiOperation(value = "新增项目信息")
    //@PermitAll
    //@PreAuthorize("@ss.hasPermission('system:user:create')")
    public CommonResult<String> createProjectInfo(@Valid @RequestBody ProjectCreateReq reqVO) {
        return CommonResult.success(projectInfoService.createProjectInfo(reqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改项目信息")
    //@PermitAll
    //@PreAuthorize("@ss.hasPermission('system:user:update')")
    public CommonResult<Boolean> updateProjectInfo(@Valid @RequestBody ProjectUpdateReq reqVO) {
        projectInfoService.updateProjectInfo(reqVO);
        return CommonResult.success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除项目信息")
    //@PermitAll
    //@PreAuthorize("@ss.hasPermission('system:user:delete')")
    public CommonResult<Boolean> deleteProjectInfo(@ApiParam(name = "sid", value = "123", required = true) @RequestParam("sid") String sid) {
        if (StringUtils.isBlank(sid)) {
            throw new ServiceException(SystemErrorEnum.PARAM_IS_NULL);
        }
        projectInfoService.deleteProjectInfo(sid);
        return CommonResult.success(true);
    }

    @PostMapping("/update-mapper")
    @ApiOperation(value = "修改项目信息结构映射")
    //@PermitAll
    //@PreAuthorize("@ss.hasPermission('system:user:update')")
    public CommonResult<Boolean> updateProjectInfoMapper(@Valid @RequestBody ProjectMapperUpdateReq reqVO) {
        projectInfoService.updateProjectInfoMapper(reqVO);
        return CommonResult.success(true);
    }

    //--------模板相关--------
    //@Operation(summary = "查询模板信息集合")
    //@GetMapping(value = "/template-list")

    @GetMapping("/template-list")
    @ApiOperation(value = "查询采集模板信息集合")
    //@PermitAll
    public CommonResult<List<FlatTemplateRsp>> getCollectionFlatTemplateList() {
        return CommonResult.success(projectInfoService.getCollectionFlatTemplateList());
    }

    @PostMapping("/template-create")
    @ApiOperation(value = "新增采集模板信息")
    //@PermitAll
    //@PreAuthorize("@ss.hasPermission('system:user:create')")
    public CommonResult<String> createCollectionFlatTemplate(@Valid @RequestBody CollectionFlatTemplateCreateReq reqVO) {
        return CommonResult.success(projectInfoService.createCollectionFlatTemplate(reqVO));
    }

    @GetMapping("/template-delete")
    @ApiOperation(value = "删除采集模板信息")
    //@PermitAll
    //@PreAuthorize("@ss.hasPermission('system:user:delete')")
    public CommonResult<Boolean> deleteCollectionFlatTemplate(@ApiParam(name = "sid", value = "123", required = true) @RequestParam("sid") String sid) {
        if (StringUtils.isBlank(sid)) {
            throw new ServiceException(SystemErrorEnum.PARAM_IS_NULL);
        }
        projectInfoService.deleteCollectionFlatTemplate(sid);
        return CommonResult.success(true);
    }

    @GetMapping("/list-sketch")
    @ApiOperation(value = "获得项目概述列表")
    //@PermitAll
    //@PreAuthorize("@ss.hasPermission('system:user:list')")
    public CommonResult<List<ProjectSketchRsp>> getProjectSketchList() {
        // 获得用户分页列表
        return CommonResult.success(projectInfoService.getProjectSketchList());
    }

}
