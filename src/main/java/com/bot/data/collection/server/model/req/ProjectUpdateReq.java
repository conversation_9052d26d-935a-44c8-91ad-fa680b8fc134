package com.bot.data.collection.server.model.req;

import com.bot.data.collection.server.model.base.ProjectInfoBaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
@ApiModel("项目信息更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ProjectUpdateReq extends ProjectInfoBaseVO {

    @ApiModelProperty(value = "1024", name = "sid", required = true)
    @NotEmpty(message = "sid不能为空")
    private String sid;
}
