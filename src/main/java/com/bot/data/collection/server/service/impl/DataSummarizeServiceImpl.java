package com.bot.data.collection.server.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bot.data.collection.common.exception.ServiceException;
import com.bot.data.collection.common.pojo.PageResult;
import com.bot.data.collection.server.constants.*;
import com.bot.data.collection.server.convert.CollectionTaskConvertor;
import com.bot.data.collection.server.mapper.CollectionTaskMapper;
import com.bot.data.collection.server.mapper.es.CollectionMapper;
import com.bot.data.collection.server.model.dto.CollectionFlatDTO;
import com.bot.data.collection.server.model.dto.TaskPageReqDTO;
import com.bot.data.collection.server.model.entity.CollectionTaskDO;
import com.bot.data.collection.server.model.req.DataStatReq;
import com.bot.data.collection.server.model.req.TaskDataInfoPageReq;
import com.bot.data.collection.server.model.req.TaskSummarizePageReq;
import com.bot.data.collection.server.model.rsp.DataStatRsp;
import com.bot.data.collection.server.model.rsp.TaskSummarizeRsp;
import com.bot.data.collection.server.service.DataSummarizeService;
import com.bot.data.collection.server.service.ProjectInfoService;
import com.bot.data.collection.system.utils.LoginUser;
import com.bot.data.collection.system.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Service
public class DataSummarizeServiceImpl implements DataSummarizeService {
    @Resource
    CollectionTaskMapper collectionTaskMapper;
    @Resource
    CollectionMapper collectionMapper;
    @Resource
    CollectionTaskConvertor collectionTaskConvertor;
    @Resource
    ProjectInfoService projectInfoService;
    @Override
    public PageResult<TaskSummarizeRsp> getTaskSummarizePage(TaskSummarizePageReq reqVO) {
        LoginUser loginUser = SecurityUtils.getLoginUser();

        //TODO: 测试参数
        /*loginUser = new LoginUser();
        loginUser.setSid("51ee8e133cbf018e1343dd5f9e788fd0");*/
        TaskPageReqDTO taskPageReqDTO = collectionTaskConvertor.convert(reqVO);
        taskPageReqDTO.setType(AuditorTypeEnum.TWO_AUDITOR.getCode());
        taskPageReqDTO.setUserSid(loginUser.getSid());

        Page<CollectionTaskDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        List<CollectionTaskDO> list = collectionTaskMapper.selectTaskPage(page, taskPageReqDTO);
        PageResult<TaskSummarizeRsp> rsp = new PageResult<>();
        rsp.setTotal(page.getTotal());
        rsp.setRecords(collectionTaskConvertor.convert(list));

        //任务状态，开始时间，最后新增时间 今日新增数据量 总数据量 （入库数据量 导出数据量）
        taskSummarizeUtil(rsp.getRecords());
        //拼接索引
        return rsp;
    }


    @Override
    public PageResult<Map<String, Object>> getCollectDataInfoPage(TaskDataInfoPageReq reqVO) {
        CollectionTaskDO collectionTaskDO = collectionTaskMapper.selectById(reqVO.getTaskSid());
        if (null == collectionTaskDO){
            throw new ServiceException(ServerErrorEnum.TASK_NOT_EXISTS);
        }
        //TODO: 测试参数
        /*collectionTaskDO.setSource(0);
        collectionTaskDO.setCiCodePath("888_888_080_888");*/
        String index = collectionMapper.getEsIndex(collectionTaskDO.getSource(), collectionTaskDO.getCiCodePath());
        //String index = CollectionMapper.getEsIndex(collectionTaskDO.getSource(), collectionTaskDO.getCiCodePath());
        PageResult<Map<String, Object>> rsp = collectionMapper.selectTaskDataInfoPage(reqVO, index);
        if (CollUtil.isNotEmpty(rsp.getRecords())){
            rsp.getRecords().forEach(map -> {
                map.put(EsFieldConstant.AUDIT_STATUS, AuditStatusEnum.getValue(Integer.parseInt(map.get(EsFieldConstant.AUDIT_STATUS).toString())));
            });
        }
        return rsp;
    }

    @Override
    public List<CollectionFlatDTO> getCollectDataMappingDict(String taskSid) {
        CollectionTaskDO collectionTaskDO = collectionTaskMapper.selectById(taskSid);
        if (null == collectionTaskDO){
            throw new ServiceException(ServerErrorEnum.TASK_NOT_EXISTS);
        }
        return projectInfoService.getCollectDataMappingDict(collectionTaskDO.getPiSid());
    }

    @Override
    public DataStatRsp getDataStat(DataStatReq reqVO) {
        CollectionTaskDO collectionTaskDO = collectionTaskMapper.selectById(reqVO.getTaskSid());
        if (null == collectionTaskDO){
            throw new ServiceException(ServerErrorEnum.TASK_NOT_EXISTS);
        }

        DataStatRsp rsp = collectionTaskConvertor.convertDataStatRsp(collectionTaskDO);
        String index = collectionMapper.getEsIndex(collectionTaskDO.getSource(), collectionTaskDO.getCiCodePath());
        rsp.setStats(collectionMapper.selectTaskDataStat(reqVO, index));
        return rsp;
    }

    /**
     * 任务汇总帮助类
     * @param list
     */
    private void taskSummarizeUtil(List<TaskSummarizeRsp> list) {
        if (CollUtil.isNotEmpty(list)) {
            for (TaskSummarizeRsp req : list) {
                //测试参数
                /*req.setSid("4028b88187b7d84c0187b7d84d740009");
                req.setSource(1);
                req.setCiCodePath("000_001_002_001");*/

                String index = collectionMapper.getEsIndex(req.getSource(), req.getCiCodePath());
                if (StringUtils.isNotEmpty(index)){
                    //根据任务sid，索引查询相关统计信息
                    req.setTotal(collectionMapper.selectTaskDataTotal(index, req.getSid()));
                    req.setTodayAddCount(collectionMapper.selectTaskDataTodayAddCount(index, req.getSid()));
                }
                req.setTaskStatusValue(TaskStatusEnum.getValue(req.getTaskStatus()));
            }
        }
    }


}
