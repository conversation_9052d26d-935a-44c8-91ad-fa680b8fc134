package com.bot.data.collection.server.mapper.es;

import cn.hutool.core.collection.CollUtil;
import com.bot.data.collection.common.exception.ServiceException;
import com.bot.data.collection.common.pojo.PageResult;
import com.bot.data.collection.common.utils.DateUtils;
import com.bot.data.collection.common.utils.ElasticsearchTemplate;
import com.bot.data.collection.server.constants.*;
import com.bot.data.collection.server.model.dto.DataTermDTO;
import com.bot.data.collection.server.model.es.EsBucketDTO;
import com.bot.data.collection.server.model.req.DataStatReq;
import com.bot.data.collection.server.model.req.TaskDataInfoPageReq;
import com.bot.data.collection.server.model.req.TaskDataInfoQueryReq;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.histogram.DateHistogramAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.histogram.DateHistogramInterval;
import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Repository
public class CollectionMapper {
    private final DateTimeFormatter dfDay = DateTimeFormatter.ofPattern(DateUtils.FORMAT_YEAR_MONTH_DAY);
    @Resource
    private ElasticsearchTemplate elasticsearchTemplate;

    /**
     * 查询当前任务总数据条数
     * @param index
     * @param taskSid
     * @return
     */
    public long selectTaskDataTotal(String index, String taskSid) {
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(QueryBuilders.termQuery(EsFieldConstant.COLLECTION_TASK_SID, taskSid));
        queryBuilder.filter(QueryBuilders.rangeQuery(EsFieldConstant.CREATE_TIME).gt(DateUtils.ES_DELETE_TIME));
        return elasticsearchTemplate.findPageTotal(index, queryBuilder);
    }

    /**
     * 查询今日新增数据条数
     * @param index
     * @param taskSid
     */
    public Long selectTaskDataTodayAddCount(String index, String taskSid) {
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(QueryBuilders.termQuery(EsFieldConstant.COLLECTION_TASK_SID, taskSid));
        queryBuilder.filter(QueryBuilders.rangeQuery(EsFieldConstant.CREATE_TIME)
                .gte(dfDay.format(LocalDateTime.now()) + DateUtils.FORMAT_ZERO_HOUR_MINUTE_SECOND)
        );
        return elasticsearchTemplate.findPageTotal(index, queryBuilder);

    }


    /**
     * 根据任务sid、数据状态查询数据
     * @param index
     * @param taskSid
     * @param auditStatus
     * @return
     */
    public Long selectTaskDataCountByStatus(String index, String taskSid, int auditStatus) {
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(QueryBuilders.termQuery(EsFieldConstant.COLLECTION_TASK_SID, taskSid));
        queryBuilder.must(QueryBuilders.termQuery(EsFieldConstant.AUDIT_STATUS, auditStatus));
        queryBuilder.filter(QueryBuilders.rangeQuery(EsFieldConstant.CREATE_TIME).gt(DateUtils.ES_DELETE_TIME));
        return elasticsearchTemplate.findPageTotal(index, queryBuilder);
    }

    /**
     * 查询任务数据分页
     * @param reqVO
     * @param index
     * @return
     */
    public PageResult<Map<String, Object>> selectTaskDataInfoPage(TaskDataInfoPageReq reqVO, String index) {
        PageResult<Map<String, Object>> pageResult = new PageResult<>();
        //拼接查询条件
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(QueryBuilders.termQuery(EsFieldConstant.COLLECTION_TASK_SID, reqVO.getTaskSid()));
        if (reqVO.getAuditStatus() != null) {
            queryBuilder.must(QueryBuilders.termQuery(EsFieldConstant.AUDIT_STATUS, reqVO.getAuditStatus().toString()));
        }
        if (CollUtil.isNotEmpty(reqVO.getDataTermDTOS())) {
            for (DataTermDTO dto : reqVO.getDataTermDTOS()) {
                if (dto.getType().intValue() == EsTypeEnum.KEYWORD.getCode().intValue() || dto.getType().intValue() == EsTypeEnum.TEXT.getCode().intValue()) {
                    queryBuilder.must(QueryBuilders.termQuery(dto.getKey(), dto.getValue()));
                }
            }
        }
        if (StringUtils.isNotEmpty(reqVO.getFirstAuditor())) {
            queryBuilder.must(QueryBuilders.termQuery(EsFieldConstant.FIRST_AUDITOR, reqVO.getFirstAuditor()));
        }
        if (StringUtils.isNotEmpty(reqVO.getTwoAuditor())) {
            queryBuilder.must(QueryBuilders.termQuery(EsFieldConstant.TWO_AUDITOR, reqVO.getTwoAuditor()));
        }
        if (reqVO.getReleaseTimeStart() != null || reqVO.getReleaseTimeEnd() != null) {
            RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery(EsFieldConstant.RELEASE_TIME);
            if (reqVO.getReleaseTimeStart() != null) {
                rangeQueryBuilder.gte(reqVO.getReleaseTimeStart());
            }
            if (reqVO.getReleaseTimeEnd() != null) {
                rangeQueryBuilder.lte(reqVO.getReleaseTimeEnd());
            }
            queryBuilder.filter(rangeQueryBuilder);
        }

        if (reqVO.getCreateTimeStart() != null || reqVO.getCreateTimeEnd() != null) {
            RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery(EsFieldConstant.CREATE_TIME);
            if (reqVO.getCreateTimeStart() != null) {
                rangeQueryBuilder.gte(reqVO.getCreateTimeStart());
            }else {
                rangeQueryBuilder.gt(DateUtils.ES_DELETE_TIME);
            }
            if (reqVO.getCreateTimeEnd() != null) {
                rangeQueryBuilder.lte(reqVO.getCreateTimeEnd());
            }
            queryBuilder.filter(rangeQueryBuilder);
        }else {
            RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery(EsFieldConstant.CREATE_TIME);
            rangeQueryBuilder.gt(DateUtils.ES_DELETE_TIME);
            queryBuilder.filter(rangeQueryBuilder);
        }
        //查询总条数
        Long totalCount = elasticsearchTemplate.findPageTotal(index, queryBuilder);
        if (totalCount != null && totalCount > 0) {
            //排序条件
            List<SortBuilder> sortBuilders = new ArrayList<>();
            SortBuilder sortBuilder = SortBuilders.fieldSort(EsFieldConstant.CREATE_TIME)
                    .order(SortOrder.DESC);
            sortBuilders.add(sortBuilder);

            int pageNo = reqVO.getPageNo();
            int pageSize = reqVO.getPageSize();
            int offset = (pageNo - 1) * pageSize;
            if (offset + pageSize > totalCount) {
                pageSize = (int) (totalCount - offset);
            }
            List<Map<String, Object>> list = elasticsearchTemplate.findPageByQuerySortList(index, queryBuilder,EsFieldConstant.CREATE_TIME, SortOrder.DESC, offset, pageSize);
            pageResult.setRecords(list);
            pageResult.setTotal(totalCount);
        }else {
            pageResult.setTotal(0L);
        }
        return pageResult;
    }

    /**
     * 根据条件查询任务数据id集合
     * @param reqVO
     * @param index
     * @return
     */
    public Set<String> selectTaskDataIdsByQuery(TaskDataInfoQueryReq reqVO, String index) {
        //拼接查询条件
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(QueryBuilders.termQuery(EsFieldConstant.COLLECTION_TASK_SID, reqVO.getTaskSid()));
        if (reqVO.getAuditStatus() != null) {
            queryBuilder.must(QueryBuilders.termQuery(EsFieldConstant.AUDIT_STATUS, reqVO.getAuditStatus().toString()));
        }
        if (CollUtil.isNotEmpty(reqVO.getDataTermDTOS())) {
            for (DataTermDTO dto : reqVO.getDataTermDTOS()) {
                if (dto.getType().intValue() == EsTypeEnum.KEYWORD.getCode().intValue() || dto.getType().intValue() == EsTypeEnum.TEXT.getCode().intValue()) {
                    queryBuilder.must(QueryBuilders.termQuery(dto.getKey(), dto.getValue()));
                }
            }
        }
        if (reqVO.getReleaseTimeStart() != null || reqVO.getReleaseTimeEnd() != null) {
            RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery(EsFieldConstant.RELEASE_TIME);
            if (reqVO.getReleaseTimeStart() != null) {
                rangeQueryBuilder.gte(reqVO.getReleaseTimeStart());
            }
            if (reqVO.getReleaseTimeEnd() != null) {
                rangeQueryBuilder.lte(reqVO.getReleaseTimeEnd());
            }
            queryBuilder.filter(rangeQueryBuilder);
        }

        if (reqVO.getCreateTimeStart() != null || reqVO.getCreateTimeEnd() != null) {
            RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery(EsFieldConstant.CREATE_TIME);
            if (reqVO.getCreateTimeStart() != null) {
                rangeQueryBuilder.gte(reqVO.getCreateTimeStart());
            }else {
                rangeQueryBuilder.gt(DateUtils.ES_DELETE_TIME);
            }
            if (reqVO.getCreateTimeEnd() != null) {
                rangeQueryBuilder.lte(reqVO.getCreateTimeEnd());
            }
            queryBuilder.filter(rangeQueryBuilder);
        }else {
            RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery(EsFieldConstant.CREATE_TIME);
            rangeQueryBuilder.gt(DateUtils.ES_DELETE_TIME);
            queryBuilder.filter(rangeQueryBuilder);
        }
        return elasticsearchTemplate.findIdsByQuery(index, queryBuilder);
    }

    /**
     * 更新数据状态（ES索引文档）
     * @param index
     * @param dataIds
     * @param auditStatus
     * @param firstAuditor
     * @param twoAuditor
     */
    public void updateDataStatus(String index, Set<String> dataIds, int auditStatus, String firstAuditor, String twoAuditor) {
        BoolQueryBuilder boolQueryBuilder =  QueryBuilders.boolQuery();
        dataIds.stream().forEach(dataId -> {
            boolQueryBuilder.should(QueryBuilders.termQuery("_id",dataId));
        });
        //修改单个
        Map<String, Object> map = new HashMap<>();
        map.put(EsFieldConstant.AUDIT_STATUS, String.valueOf(auditStatus));

        StringBuilder sb = new StringBuilder();
        sb.append("ctx._source.").append(EsFieldConstant.AUDIT_STATUS).append(" = params.").append(EsFieldConstant.AUDIT_STATUS).append(";");
        if (firstAuditor != null) {
            sb.append("ctx._source.").append(EsFieldConstant.FIRST_AUDITOR).append(" = params.").append(EsFieldConstant.FIRST_AUDITOR).append(";");
            map.put(EsFieldConstant.FIRST_AUDITOR, firstAuditor);
        }
        if (twoAuditor != null) {
            sb.append("ctx._source.").append(EsFieldConstant.TWO_AUDITOR).append(" = params.").append(EsFieldConstant.TWO_AUDITOR).append(";");
            map.put(EsFieldConstant.TWO_AUDITOR, twoAuditor);
        }
        Script script = new Script(ScriptType.INLINE, "painless",
                sb.toString(), map);
        elasticsearchTemplate.updateByQuery(index, boolQueryBuilder, script);
    }

    public void deleteDataByIds(String index, Set<String> dataIds) {
        BoolQueryBuilder boolQueryBuilder =  QueryBuilders.boolQuery();
        dataIds.stream().forEach(dataId -> {
            boolQueryBuilder.should(QueryBuilders.termQuery("_id",dataId));
        });
        //修改单个
        Map<String, Object> map = new HashMap<>();
        //String dateStr = "1900-01-01 00:00:00";//LocalDateTimeUtils.parseDateTime(dateStr)
        map.put(EsFieldConstant.CREATE_TIME, DateUtils.ES_DELETE_TIME);

        StringBuilder sb = new StringBuilder();
        sb.append("ctx._source.").append(EsFieldConstant.CREATE_TIME).append(" = params.").append(EsFieldConstant.CREATE_TIME).append(";");
        /*if (firstAuditor != null) {
            sb.append("ctx._source.").append(EsFieldConstant.FIRST_AUDITOR).append(" = params.").append(EsFieldConstant.FIRST_AUDITOR).append(";");
            map.put(EsFieldConstant.FIRST_AUDITOR, "啦啦啦");
        }
        if (twoAuditor != null) {
            sb.append("ctx._source.").append(EsFieldConstant.TWO_AUDITOR).append(" = params.").append(EsFieldConstant.TWO_AUDITOR).append(";");
            map.put(EsFieldConstant.TWO_AUDITOR, "急急急");
        }*/
        Script script = new Script(ScriptType.INLINE, "painless",
                sb.toString(), map);
        elasticsearchTemplate.updateByQuery(index, boolQueryBuilder, script);
    }


    public String getEsIndex(Integer source, String ciCodePath){
        String index = null;
        if (StringUtils.isNotEmpty(ciCodePath)){
            index = CollectionSourceEnum.getEsIndexPrefix(source);
            String[] ciCodePathArr = ciCodePath.split("_");
            for (int i = 0; i < ciCodePathArr.length; i++) {
                index += ciCodePathArr[i];
                if (i == 0){
                    index += "_";
                }
            }
        }
        if (StringUtils.isNotEmpty(index)){
            boolean indexExists = elasticsearchTemplate.existsIndex(index);
            if (!indexExists){
                throw new ServiceException(ServerErrorEnum.DATA_INFO_NOT_EXISTS);
            }
        }
        return index;
    }

    /**
     * 根据任务id和状态查询最老的文章
     * @param index
     * @param taskSid
     * @param auditStatus
     * @return
     */
    public Map<String, Object> selectTaskDataInfoByOldest(String index, String taskSid, int auditStatus) {
        //拼接查询条件
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(QueryBuilders.termQuery(EsFieldConstant.COLLECTION_TASK_SID, taskSid));
        queryBuilder.must(QueryBuilders.termQuery(EsFieldConstant.AUDIT_STATUS, String.valueOf(auditStatus)));

        RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery(EsFieldConstant.CREATE_TIME);
        rangeQueryBuilder.gt(DateUtils.ES_DELETE_TIME);
        queryBuilder.filter(rangeQueryBuilder);

        //排序条件
        List<SortBuilder> sortBuilders = new ArrayList<>();
        SortBuilder sortBuilder = SortBuilders.fieldSort(EsFieldConstant.CREATE_TIME)
                .order(SortOrder.ASC);
        sortBuilders.add(sortBuilder);

        List<Map<String, Object>> result = elasticsearchTemplate.findPageByQuerySortList(index, queryBuilder,EsFieldConstant.CREATE_TIME, SortOrder.DESC, 0, 1);
        if (CollUtil.isNotEmpty(result)){
            return result.get(0);
        }
        return null;
    }

    /**
     * 根据数据id和状态查询文章
     * @param index
     * @param dataId
     * @return
     */
    public Map<String, Object> selectTaskDataInfoByDataId(String index, String dataId) {
        return elasticsearchTemplate.getDataMapById(index, dataId);
    }

    public void updateDataById(String index, String dataId, Map<String, Object> data) {
        elasticsearchTemplate.update(index, dataId, data);
    }

    /**
     * 查询任务数据新增统计
     * @param reqVO
     * @param index
     * @return
     */
    public List<EsBucketDTO> selectTaskDataStat(DataStatReq reqVO, String index) {
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(QueryBuilders.termQuery(EsFieldConstant.COLLECTION_TASK_SID, reqVO.getTaskSid()));

        RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery(EsFieldConstant.CREATE_TIME);
        rangeQueryBuilder.gte(dfDay.format(reqVO.getCreateTimeStart()) + DateUtils.FORMAT_ZERO_HOUR_MINUTE_SECOND);
        rangeQueryBuilder.lt(dfDay.format(reqVO.getCreateTimeEnd().plusDays(1)) + DateUtils.FORMAT_ZERO_HOUR_MINUTE_SECOND);
        queryBuilder.filter(rangeQueryBuilder);

        DateHistogramAggregationBuilder aggregationBuilders = AggregationBuilders
                .dateHistogram(EsConstant.COLLECT_DATA_DAY_AGG)
                .field(EsFieldConstant.CREATE_TIME)
                .calendarInterval(DateHistogramInterval.DAY)
                .format(DateUtils.FORMAT_YEAR_MONTH_DAY)
                .minDocCount(0);

        return elasticsearchTemplate.findAggrByQuery(index, queryBuilder, aggregationBuilders);
    }
}
