package com.bot.data.collection.server.model.rsp;

import com.bot.data.collection.server.model.dto.ClassDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel(value="分类信息对象",description="分类信息对象")
@Data
public class ClassifyTreeRsp {

    @ApiModelProperty(value="true",name="是否锁定",required = true)
    private Boolean lock;

    @ApiModelProperty(name="分类对象集合")
    private List<ClassDTO> classDTOList;

}
