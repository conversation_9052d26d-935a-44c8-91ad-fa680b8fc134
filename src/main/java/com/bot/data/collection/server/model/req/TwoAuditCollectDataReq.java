package com.bot.data.collection.server.model.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.Map;

@ApiModel(value="复审提交数据对象",description="复审提交数据对象")
@Data
public class TwoAuditCollectDataReq {

    @ApiModelProperty(value="审核状态：0-待派发，1-已派发，2-待复审，3-已打回，4-已通过",name="审核状态",required = true)
    private Integer auditStatus;

    @ApiModelProperty(value="111",name="数据Id")
    @NotEmpty(message = "数据Id不能为空")
    private String dataId;

    @ApiModelProperty(value="111",name="任务sid")
    @NotEmpty(message = "任务sid不能为空")
    private String taskSid;

    @ApiModelProperty(value="001-值",name="任务数据")
    private Map<String, Object> data;

}
