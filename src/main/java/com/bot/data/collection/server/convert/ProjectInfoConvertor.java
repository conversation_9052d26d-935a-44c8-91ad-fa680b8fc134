package com.bot.data.collection.server.convert;

import com.bot.data.collection.common.pojo.PageResult;
import com.bot.data.collection.server.model.entity.ProjectInfoDO;
import com.bot.data.collection.server.model.req.ProjectCreateReq;
import com.bot.data.collection.server.model.req.ProjectUpdateReq;
import com.bot.data.collection.server.model.rsp.ProjectInfoRsp;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface ProjectInfoConvertor {
    ProjectInfoConvertor INSTANCE = Mappers.getMapper(ProjectInfoConvertor.class);

    ProjectInfoRsp convert(ProjectInfoDO projectInfoDO);

    ProjectInfoDO convert(ProjectCreateReq bean);
    ProjectInfoDO convert(ProjectUpdateReq bean);

    PageResult<ProjectInfoRsp> convert(PageResult<ProjectInfoDO> pageResult);
}
