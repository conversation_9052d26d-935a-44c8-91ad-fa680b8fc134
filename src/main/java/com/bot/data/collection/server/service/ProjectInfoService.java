package com.bot.data.collection.server.service;

import com.bot.data.collection.common.pojo.PageResult;
import com.bot.data.collection.server.model.dto.CollectionFlatDTO;
import com.bot.data.collection.server.model.req.*;
import com.bot.data.collection.server.model.rsp.FlatTemplateRsp;
import com.bot.data.collection.server.model.rsp.ProjectInfoRsp;

import java.util.List;

public interface ProjectInfoService {
    PageResult<ProjectInfoRsp> getProjectInfoPage(ProjectPageReq reqVO);

    ProjectInfoRsp getProjectInfo(String sid);

    String createProjectInfo(ProjectCreateReq reqVO);

    void updateProjectInfo(ProjectUpdateReq reqVO);

    void deleteProjectInfo(String sid);

    List<FlatTemplateRsp> getCollectionFlatTemplateList();

    String createCollectionFlatTemplate(CollectionFlatTemplateCreateReq reqVO);

    void deleteCollectionFlatTemplate(String sid);

    void updateProjectInfoMapper(ProjectMapperUpdateReq reqVO);

    List<ProjectSketchRsp> getProjectSketchList();

    List<CollectionFlatDTO> getCollectDataMappingDict(String sid);
}
