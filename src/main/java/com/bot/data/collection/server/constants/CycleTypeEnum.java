package com.bot.data.collection.server.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum CycleTypeEnum {
    LONG_HISTORY(0, "长期"),//含历史
    /*LONG_NOW(1, "长期(持续更新)"),
    LONG_SPECIFY(2, "长期(指定时间开始)"),
    TIME_INTERVAL(3, "指定时间区间"),*/
    ONCE(1, "单次"),
    TIME_INTERVAL(2, "时间段"),//指定时间区间
    UNKNOWN(-99, "未知");

    private final int code;
    private final String value;

    public static int getCode(String value){
        return Stream.of(values()).filter(b -> b.value.equals(value)).findFirst().orElse(UNKNOWN).getCode();
    }
    public static String getValue(int code){
        return Stream.of(values()).filter(b -> b.code==code).findFirst().orElse(UNKNOWN).getValue();
    }
}
