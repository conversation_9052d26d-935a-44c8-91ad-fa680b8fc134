package com.bot.data.collection.server.controller;

import com.bot.data.collection.common.exception.ServiceException;
import com.bot.data.collection.common.pojo.CommonResult;
import com.bot.data.collection.common.pojo.PageResult;
import com.bot.data.collection.server.model.dto.CollectionFlatDTO;
import com.bot.data.collection.server.model.req.*;
import com.bot.data.collection.server.model.rsp.*;
import com.bot.data.collection.server.service.DataDisposeService;
import com.bot.data.collection.server.service.DataSummarizeService;
import com.bot.data.collection.system.constants.SystemErrorEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@Api(tags = "数据结果汇总信息管理")
@RestController
@RequestMapping("/data-summarize")
@Validated
public class DataSummarizeController {
    @Resource
    DataSummarizeService dataSummarizeService;
    @Resource
    DataDisposeService dataDisposeService;

    @PostMapping("/task-page")
    @ApiOperation(value = "获得采集任务汇总分页列表")
    //@PermitAll
    //@PreAuthorize("@ss.hasPermission('system:user:list')")
    public CommonResult<PageResult<TaskSummarizeRsp>> getTaskSummarizePage(@Valid @RequestBody TaskSummarizePageReq reqVO) {
        return CommonResult.success(dataSummarizeService.getTaskSummarizePage(reqVO));
    }

    @PostMapping("/info-page")
    @ApiOperation(value = "获得任务采集数据分页信息列表")
    //@PermitAll
    public CommonResult<PageResult<Map<String, Object>>> getCollectDataInfoPage(@Valid @RequestBody TaskDataInfoPageReq reqVO) {
        return CommonResult.success(dataSummarizeService.getCollectDataInfoPage(reqVO));
    }

    @GetMapping("/mapping-dict")
    @ApiOperation(value = "获得采集数据结果字典信息")
    //@PermitAll
    //@PreAuthorize("@ss.hasPermission('system:user:list')")
    public CommonResult<List<CollectionFlatDTO>> getCollectDataMappingDict(@ApiParam(name = "taskSid", value = "123", required = true) @RequestParam("taskSid") String taskSid) {
        if (StringUtils.isBlank(taskSid)) {
            throw new ServiceException(SystemErrorEnum.PARAM_IS_NULL);
        }
        return CommonResult.success(dataSummarizeService.getCollectDataMappingDict(taskSid));
    }

    @PostMapping("/dispatch")
    @ApiOperation(value = "派发采集数据（批量）")
    //@PermitAll
    public CommonResult<Boolean> dispatchCollectDataBatch(@Valid @RequestBody CollectDataReq reqVO) {
        dataDisposeService.dispatchCollectDataBatch(reqVO);
        return CommonResult.success(true);
    }

    @PostMapping("/reject")
    @ApiOperation(value = "打回采集数据（批量）")
    //@PermitAll
    public CommonResult<Boolean> rejectCollectDataBatch(@Valid @RequestBody CollectDataReq reqVO) {
        dataDisposeService.rejectCollectDataBatch(reqVO);
        return CommonResult.success(true);
    }

    @PostMapping("/pass")
    @ApiOperation(value = "通过采集数据（批量）")
    //@PermitAll
    public CommonResult<Boolean> passCollectDataBatch(@Valid @RequestBody CollectDataReq reqVO) {
        dataDisposeService.passCollectDataBatch(reqVO);
        return CommonResult.success(true);
    }

    @PostMapping("/remove")
    @ApiOperation(value = "删除采集数据（批量）")
    //@PermitAll
    public CommonResult<Boolean> removeCollectDataBatch(@Valid @RequestBody CollectDataReq reqVO) {
        dataDisposeService.removeCollectDataBatch(reqVO);
        return CommonResult.success(true);
    }

    @PostMapping("/dispatch-all")
    @ApiOperation(value = "派发采集数据（全部）")
    //@PermitAll
    public CommonResult<Boolean> dispatchCollectDataAll(@Valid @RequestBody TaskDataInfoQueryReq reqVO) {
        dataDisposeService.dispatchCollectDataAll(reqVO);
        return CommonResult.success(true);
    }

    @PostMapping("/reject-all")
    @ApiOperation(value = "打回采集数据（全部）")
    //@PermitAll
    public CommonResult<Boolean> rejectCollectDataAll(@Valid @RequestBody TaskDataInfoQueryReq reqVO) {
        dataDisposeService.rejectCollectDataAll(reqVO);
        return CommonResult.success(true);
    }

    @PostMapping("/pass-all")
    @ApiOperation(value = "通过采集数据（全部）")
    //@PermitAll
    public CommonResult<Boolean> passCollectDataAll(@Valid @RequestBody TaskDataInfoQueryReq reqVO) {
        dataDisposeService.passCollectDataAll(reqVO);
        return CommonResult.success(true);
    }

    @PostMapping("/remove-all")
    @ApiOperation(value = "删除采集数据（全部）")
    //@PermitAll
    public CommonResult<Boolean> removeCollectDataAll(@Valid @RequestBody TaskDataInfoQueryReq reqVO) {
        dataDisposeService.removeCollectDataAll(reqVO);
        return CommonResult.success(true);
    }

    @PostMapping("/add-data-count")
    @ApiOperation(value = "获得采集新增数据量统计")
    @PermitAll
    //@PreAuthorize("@ss.hasPermission('system:user:list')")
    public CommonResult<DataStatRsp> getDataStat(@Valid @RequestBody DataStatReq reqVO) {
        return CommonResult.success(dataSummarizeService.getDataStat(reqVO));
    }
}
