package com.bot.data.collection.server.model.rsp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@ApiModel(value="任务汇总对象",description="任务汇总对象")
@Data
public class TaskDisposeRsp {

    @ApiModelProperty(value="11111",name="项目sid",required=true)
    private String sid;
    @ApiModelProperty(value="11111",name="任务标识",required=true)
    private String sno;
    @ApiModelProperty(value="11111",name="任务名称")
    private String name;
    @ApiModelProperty(name="关联项目名称/编号")
    private String project;
    @ApiModelProperty(name="任务分类")
    private String ciNames;
    @ApiModelProperty(value="111",name="待处理条数")
    private Long pendCount;

    @ApiModelProperty(value="001",name="（任务分类）数据分类编号路径")
    private String ciCodePath;
    @ApiModelProperty(value="0",name="采集方式（获取来源）：0-本地脚本 1-三方接口（清博）")
    private Integer source;
}
