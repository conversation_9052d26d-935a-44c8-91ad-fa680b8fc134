package com.bot.data.collection.server.service;

import com.bot.data.collection.server.model.dto.ClassDTO;
import com.bot.data.collection.server.model.req.ClassifyCreateReq;
import com.bot.data.collection.server.model.req.ClassifyUpdateReq;

import java.util.List;

public interface ClassifyInfoService {
    List<ClassDTO> getClassifyLibraryList();

    Boolean getClassifyLockAdd();

    String createClassifyInfo(ClassifyCreateReq reqVO);

    void updateClassifyInfo(ClassifyUpdateReq reqVO);
    void updateClassifyLock(Boolean lock);


    //void importClassifyData();
}
