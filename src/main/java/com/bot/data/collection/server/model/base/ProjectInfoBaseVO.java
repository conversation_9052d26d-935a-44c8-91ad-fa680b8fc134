package com.bot.data.collection.server.model.base;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class ProjectInfoBaseVO {

    @ApiModelProperty(value="信息",name="项目名称",required=true)
    @NotEmpty(message = "项目名称名称不能为空")
    private String piName;

    @ApiModelProperty(value="项目说明",name="项目说明")
    private String piDesc;

    @ApiModelProperty(value="0-内部数据 1-业务数据 2-转出数据",name="项目类型",required=true)
    @NotNull(message = "项目类型不能为空")
    private Integer piType;

    @ApiModelProperty(value="false",name="是否人审：0-否，1-是")
    private Boolean peopleAudit;

    @ApiModelProperty(value="[1,2,3]",name="初审人用户sid集合")
    private List<String> firstAuditorUserSids;

    @ApiModelProperty(value="[1,2,3]",name="初审人用户组sid集合")
    private List<String> firstAuditorUserGroupSids;

    @ApiModelProperty(value="[1,2,3]",name="复审人用户sid集合")
    private List<String> twoAuditorUserSids;

    @ApiModelProperty(value="[1,2,3]",name="复审人用户组sid集合")
    private List<String> twoAuditorUserGroupSids;
}
