package com.bot.data.collection.server.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bot.data.collection.common.exception.ServiceException;
import com.bot.data.collection.common.pojo.PageResult;
import com.bot.data.collection.common.utils.DateUtils;
import com.bot.data.collection.common.utils.LocalDateTimeUtils;
import com.bot.data.collection.common.utils.UUIDUtils;
import com.bot.data.collection.server.constants.*;
import com.bot.data.collection.server.convert.CollectionTaskConvertor;
import com.bot.data.collection.server.convert.DataAuditHistoryConvertor;
import com.bot.data.collection.server.mapper.CollectionTaskMapper;
import com.bot.data.collection.server.mapper.DataAuditHistoryMapper;
import com.bot.data.collection.server.mapper.es.CollectionMapper;
import com.bot.data.collection.server.model.dto.DataTermDTO;
import com.bot.data.collection.server.model.dto.TaskPageReqDTO;
import com.bot.data.collection.server.model.entity.CollectionTaskDO;
import com.bot.data.collection.server.model.entity.DataAuditHistoryDO;
import com.bot.data.collection.server.model.req.*;
import com.bot.data.collection.server.model.rsp.DataInfoRsp;
import com.bot.data.collection.server.model.rsp.TaskDisposeRsp;
import com.bot.data.collection.server.model.rsp.UserDataCountRsp;
import com.bot.data.collection.server.service.DataDisposeService;
import com.bot.data.collection.server.service.ProjectInfoService;
import com.bot.data.collection.system.utils.LoginUser;
import com.bot.data.collection.system.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Slf4j
@Service
public class DataDisposeServiceImpl implements DataDisposeService {
    private final DateTimeFormatter dfDay = DateTimeFormatter.ofPattern(DateUtils.FORMAT_YEAR_MONTH_DAY);
    @Resource
    CollectionTaskMapper collectionTaskMapper;
    @Resource
    CollectionMapper collectionMapper;
    @Resource
    DataAuditHistoryMapper dataAuditHistoryMapper;
    @Resource
    CollectionTaskConvertor collectionTaskConvertor;
    @Resource
    ProjectInfoService projectInfoService;
    @Resource
    DataAuditHistoryConvertor dataAuditHistoryConvertor;

    @Override
    public PageResult<TaskDisposeRsp> getTaskDisposePage(TaskDisposePageReq reqVO) {
        //获取当前用户、用户所在组初审相关的
        LoginUser loginUser = SecurityUtils.getLoginUser();
        /*loginUser = new LoginUser();
        loginUser.setSid("51ee8e133cbf018e1343dd5f9e788fd0");*/

        TaskPageReqDTO taskPageReqDTO = collectionTaskConvertor.convert(reqVO);
        taskPageReqDTO.setType(AuditorTypeEnum.FIRST_AUDITOR.getCode());
        taskPageReqDTO.setUserSid(loginUser.getSid());

        Page<CollectionTaskDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        List<CollectionTaskDO> list = collectionTaskMapper.selectTaskPage(page, taskPageReqDTO);
        PageResult<TaskDisposeRsp> rsp = new PageResult<>();
        rsp.setTotal(page.getTotal());
        rsp.setRecords(collectionTaskConvertor.convertTaskDisposeRsp(list));
        taskDisposeUtil(rsp.getRecords());
        return rsp;
    }

    @Override
    public UserDataCountRsp getUserDataCount() {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        /*loginUser = new LoginUser();
        loginUser.setSid("51ee8e133cbf018e1343dd5f9e788fd0");*/

        UserDataCountRsp rsp = new UserDataCountRsp();
        rsp.setAwaitDisposeCount(dataAuditHistoryMapper.selectAwaitDisposeCountByUserSid(loginUser.getSid()));
        String todayTime = dfDay.format(LocalDateTime.now()) + DateUtils.FORMAT_ZERO_HOUR_MINUTE_SECOND;

        rsp.setTodayDisposeCount(dataAuditHistoryMapper.selectTodayDisposeCountByUserSid(loginUser.getSid(), LocalDateTimeUtils.parseDateTime(todayTime)));
        rsp.setRejectCount(dataAuditHistoryMapper.selectRejectCountByUserSid(loginUser.getSid()));
        rsp.setAlreadyDisposeCount(dataAuditHistoryMapper.selectAlreadyDisposeCountByUserSid(loginUser.getSid()));

        return rsp;
    }

    @Override
    public DataInfoRsp getDataInfo(String taskSid) {
//        taskSid = "51ee89d955aa0189d955b81bb7e8dec4";
        CollectionTaskDO taskDO = collectionTaskMapper.selectById(taskSid);
        if (null == taskDO){
            throw new ServiceException(ServerErrorEnum.TASK_NOT_EXISTS);
        }

        int auditStatus = AuditStatusEnum.ALREADY_DISPATCH.getCode();
        LoginUser loginUser = SecurityUtils.getLoginUser();
        //TODO: 测试参数
        /*taskDO.setSource(0);
        taskDO.setCiCodePath("888_888_080_888");*/
        //taskDO.setCiCodePath("888_888_080_8882");

        /*loginUser = new LoginUser();
        loginUser.setSid("51ee8e133cbf018e1343dd5f9e788fd0");*/

        DataInfoRsp rsp = new DataInfoRsp();
        String index = collectionMapper.getEsIndex(taskDO.getSource(), taskDO.getCiCodePath());
        /*if (StringUtils.isBlank(index)) {
            throw new ServiceException(ServerErrorEnum.DATA_INFO_NOT_EXISTS);
        }*/
        //index = "local_000000";
        rsp.setData(collectionMapper.selectTaskDataInfoByOldest(index, taskSid, auditStatus));
        rsp.setFlatDTOS(projectInfoService.getCollectDataMappingDict(taskDO.getPiSid()));
        rsp.setAwaitDisposeCount(dataAuditHistoryMapper.selectTaskAwaitDisposeCountByUserSid(taskSid));
        rsp.setAlreadyDisposeCount(dataAuditHistoryMapper.selectTaskAlreadyDisposeCountByUserSid(loginUser.getSid(), taskSid));
        return rsp;
    }

    @Override
    public DataInfoRsp getDataInfo(String dataId, String taskSid) {
        CollectionTaskDO taskDO = collectionTaskMapper.selectById(taskSid);
        if (null == taskDO){
            throw new ServiceException(ServerErrorEnum.TASK_NOT_EXISTS);
        }
        //TODO: 测试参数
        /*taskDO.setSource(0);
        taskDO.setCiCodePath("888_888_080_888");*/

        DataInfoRsp rsp = new DataInfoRsp();
        String index = collectionMapper.getEsIndex(taskDO.getSource(), taskDO.getCiCodePath());
        rsp.setData(collectionMapper.selectTaskDataInfoByDataId(index, dataId));
        rsp.setFlatDTOS(projectInfoService.getCollectDataMappingDict(taskDO.getPiSid()));
        List<DataAuditHistoryDO> auditHistoryDTOS = dataAuditHistoryMapper.selectHistoryList(dataId);
        if (CollUtil.isNotEmpty(auditHistoryDTOS)){
            rsp.setAuditHistoryDTOS(dataAuditHistoryConvertor.convert(auditHistoryDTOS));
        }
        return rsp;
    }

    @Transactional
    @Override
    public void dispatchCollectDataBatch(CollectDataReq reqVO) {
        if (dataAuditHistoryMapper.selectNotDispatchDataCount(reqVO.getDataIds()) > 0){
            throw new ServiceException(ServerErrorEnum.DATA_NOT_AWAIT_DISPATCH);
        }
        CollectionTaskDO taskDO = collectionTaskMapper.selectById(reqVO.getTaskSid());
        if (null == taskDO){
            throw new ServiceException(ServerErrorEnum.TASK_NOT_EXISTS);
        }
        String index = collectionMapper.getEsIndex(taskDO.getSource(), taskDO.getCiCodePath());
        int auditStatus = AuditStatusEnum.ALREADY_DISPATCH.getCode();

        //TODO: 测试参数
        LoginUser loginUser = SecurityUtils.getLoginUser();
        /*loginUser = new LoginUser();
        loginUser.setSid("51ee8e133cbf018e1343dd5f9e788fd0");
        loginUser.setName("张三三");
        taskDO.setSource(0);
        taskDO.setCiCodePath("888_888_080_888");*/

        //批量新增审核状态
        dataAuditHistoryMapper.insertBatch(returnDataAuditHistoryList(reqVO.getDataIds(), reqVO.getTaskSid(), auditStatus, loginUser.getName()));

        //更新es里面文档数据的状态
        collectionMapper.updateDataStatus(index, reqVO.getDataIds(), auditStatus, null, null);
    }

    @Transactional
    @Override
    public void rejectCollectDataBatch(CollectDataReq reqVO) {
        /*if (dataAuditHistoryMapper.selectNotRejectDataCount(reqVO.getDataIds()) > 0){
            throw new ServiceException(ServerErrorEnum.DATA_NOT_AWAIT_TWO_AUDIT);
        }*/
        if (dataAuditHistoryMapper.selectAwaitTwoAuditDataCount(reqVO.getDataIds()) < reqVO.getDataIds().size()){
            throw new ServiceException(ServerErrorEnum.DATA_NOT_AWAIT_TWO_AUDIT);
        }

        CollectionTaskDO taskDO = collectionTaskMapper.selectById(reqVO.getTaskSid());
        if (null == taskDO){
            throw new ServiceException(ServerErrorEnum.TASK_NOT_EXISTS);
        }
        String index = collectionMapper.getEsIndex(taskDO.getSource(), taskDO.getCiCodePath());

        int auditStatus = AuditStatusEnum.ALREADY_AUDITOR_REJECT.getCode();
        LoginUser loginUser = SecurityUtils.getLoginUser();
        //TODO: 测试参数
        /*loginUser = new LoginUser();
        loginUser.setSid("51ee8e133cbf018e1343dd5f9e788fd0");
        loginUser.setName("张三三");
        taskDO.setSource(0);
        taskDO.setCiCodePath("888_888_080_888");*/

        //需要将上一个状态的 isNewest 改为 1
        dataAuditHistoryMapper.updateIsNewestByDataIds(reqVO.getDataIds());
        //批量新增审核状态
        dataAuditHistoryMapper.insertBatch(returnDataAuditHistoryList(reqVO.getDataIds(), reqVO.getTaskSid(), auditStatus, loginUser.getName()));

        //更新es里面文档数据的状态
        //String index = CollectionMapper.getEsIndex(taskDO.getSource(), taskDO.getCiCodePath());
        collectionMapper.updateDataStatus(index, reqVO.getDataIds(), auditStatus, null, null);
    }

    @Transactional
    @Override
    public void passCollectDataBatch(CollectDataReq reqVO) {
        /*if (dataAuditHistoryMapper.selectNotPassDataCount(reqVO.getDataIds()) > 0){
            throw new ServiceException(ServerErrorEnum.DATA_NOT_AWAIT_TWO_AUDIT);
        }*/
        if (dataAuditHistoryMapper.selectAwaitTwoAuditDataCount(reqVO.getDataIds()) < reqVO.getDataIds().size()){
            throw new ServiceException(ServerErrorEnum.DATA_NOT_AWAIT_TWO_AUDIT);
        }

        CollectionTaskDO taskDO = collectionTaskMapper.selectById(reqVO.getTaskSid());
        if (null == taskDO){
            throw new ServiceException(ServerErrorEnum.TASK_NOT_EXISTS);
        }
        String index = collectionMapper.getEsIndex(taskDO.getSource(), taskDO.getCiCodePath());

        int auditStatus = AuditStatusEnum.ALREADY_AUDIT_PASS.getCode();
        LoginUser loginUser = SecurityUtils.getLoginUser();
        //TODO: 测试参数
        /*loginUser = new LoginUser();
        loginUser.setSid("51ee8e133cbf018e1343dd5f9e788fd0");
        loginUser.setName("张三三");
        taskDO.setSource(0);
        taskDO.setCiCodePath("888_888_080_888");*/

        //需要将上一个状态的 isNewest 改为 1
        dataAuditHistoryMapper.updateIsNewestByDataIds(reqVO.getDataIds());
        //批量新增审核状态
        dataAuditHistoryMapper.insertBatch(returnDataAuditHistoryList(reqVO.getDataIds(), reqVO.getTaskSid(), auditStatus, loginUser.getName()));

        //更新es里面文档数据的状态
        //String index = CollectionMapper.getEsIndex(taskDO.getSource(), taskDO.getCiCodePath());
        collectionMapper.updateDataStatus(index, reqVO.getDataIds(), auditStatus, null, loginUser.getName());
    }

    @Transactional
    @Override
    public void removeCollectDataBatch(CollectDataReq reqVO) {
        CollectionTaskDO taskDO = collectionTaskMapper.selectById(reqVO.getTaskSid());
        if (null == taskDO){
            throw new ServiceException(ServerErrorEnum.TASK_NOT_EXISTS);
        }
        String index = collectionMapper.getEsIndex(taskDO.getSource(), taskDO.getCiCodePath());

        dataAuditHistoryMapper.deleteByDataIds(reqVO.getDataIds());
        //LoginUser loginUser = SecurityUtils.getLoginUser();
        //TODO: 测试参数
        /*loginUser = new LoginUser();
        loginUser.setSid("51ee8e133cbf018e1343dd5f9e788fd0");
        loginUser.setName("张三三");
        taskDO.setSource(0);
        taskDO.setCiCodePath("888_888_080_888");*/
        //删除es里面文档数据
        //String index = CollectionMapper.getEsIndex(taskDO.getSource(), taskDO.getCiCodePath());
        collectionMapper.deleteDataByIds(index, reqVO.getDataIds());
    }

    @Transactional
    @Override
    public void auditCollectData(DataFirstAuditReq reqVO) {
        String dataId = reqVO.getDataId();
        String taskSid = reqVO.getTaskSid();
        if (dataAuditHistoryMapper.selectNotFirstAuditStatusDataCount(dataId) > 0){
            throw new ServiceException(ServerErrorEnum.DATA_NOT_ALREADY_DISPATCH);
        }

        CollectionTaskDO taskDO = collectionTaskMapper.selectById(taskSid);
        if (null == taskDO){
            throw new ServiceException(ServerErrorEnum.TASK_NOT_EXISTS);
        }
        String index = collectionMapper.getEsIndex(taskDO.getSource(), taskDO.getCiCodePath());

        int auditStatus = AuditStatusEnum.AWAIT_TWO_AUDIT.getCode();
        LoginUser loginUser = SecurityUtils.getLoginUser();
        //TODO: 测试参数
        /*loginUser = new LoginUser();
        loginUser.setSid("51ee8e133cbf018e1343dd5f9e788fd0");
        loginUser.setName("张三三");
        taskDO.setSource(0);
        taskDO.setCiCodePath("888_888_080_888");*/

        Set<String> dataIds = new HashSet<>(1);
        dataIds.add(dataId);
        //需要将上一个状态的 isNewest 改为 1
        dataAuditHistoryMapper.updateIsNewestByDataIds(dataIds);
        //批量新增审核状态
        DataAuditHistoryDO dataAuditHistoryDO = new DataAuditHistoryDO();
        dataAuditHistoryDO.setSid(UUIDUtils.getUUID());
        dataAuditHistoryDO.setDataId(dataId);
        dataAuditHistoryDO.setTaskSid(taskSid);
        dataAuditHistoryDO.setAuditStatus(auditStatus);
        dataAuditHistoryDO.setCreatorName(loginUser.getName());
        dataAuditHistoryMapper.insert(dataAuditHistoryDO);

        //更新es里面文档数据的状态
        //String index = CollectionMapper.getEsIndex(taskDO.getSource(), taskDO.getCiCodePath());
        collectionMapper.updateDataStatus(index, dataIds, auditStatus, loginUser.getName(), null);
    }

    @Transactional
    @Override
    public void dispatchCollectDataAll(TaskDataInfoQueryReq reqVO) {
        CollectionTaskDO taskDO = collectionTaskMapper.selectById(reqVO.getTaskSid());
        if (null == taskDO){
            throw new ServiceException(ServerErrorEnum.TASK_NOT_EXISTS);
        }
        String index = collectionMapper.getEsIndex(taskDO.getSource(), taskDO.getCiCodePath());
        int auditStatus = AuditStatusEnum.ALREADY_DISPATCH.getCode();
        //String index = CollectionMapper.getEsIndex(taskDO.getSource(), taskDO.getCiCodePath());
        Set<String> dataIds = collectionMapper.selectTaskDataIdsByQuery(reqVO, index);

        if (CollUtil.isEmpty(dataIds)){
            throw new ServiceException(ServerErrorEnum.DATA_NOT_EXISTS_AWAIT_DISPATCH);
        }
        //TODO: 测试参数
        LoginUser loginUser = SecurityUtils.getLoginUser();
        /*loginUser = new LoginUser();
        loginUser.setSid("51ee8e133cbf018e1343dd5f9e788fd0");
        loginUser.setName("张三三");
        taskDO.setSource(0);
        taskDO.setCiCodePath("888_888_080_888");*/

        //批量新增审核状态
        dataAuditHistoryMapper.insertBatch(returnDataAuditHistoryList(dataIds, reqVO.getTaskSid(), auditStatus, loginUser.getName()));

        //更新es里面文档数据的状态
        collectionMapper.updateDataStatus(index, dataIds, auditStatus, null, null);

    }

    @Transactional
    @Override
    public void rejectCollectDataAll(TaskDataInfoQueryReq reqVO) {
        CollectionTaskDO taskDO = collectionTaskMapper.selectById(reqVO.getTaskSid());
        if (null == taskDO){
            throw new ServiceException(ServerErrorEnum.TASK_NOT_EXISTS);
        }
        String index = collectionMapper.getEsIndex(taskDO.getSource(), taskDO.getCiCodePath());
        //String index = CollectionMapper.getEsIndex(taskDO.getSource(), taskDO.getCiCodePath());
        Set<String> dataIds = collectionMapper.selectTaskDataIdsByQuery(reqVO, index);

        if (CollUtil.isEmpty(dataIds)){
            throw new ServiceException(ServerErrorEnum.DATA_NOT_EXISTS_AWAIT_TWO_AUDIT);
        }

        int auditStatus = AuditStatusEnum.ALREADY_AUDITOR_REJECT.getCode();
        LoginUser loginUser = SecurityUtils.getLoginUser();
        //TODO: 测试参数
        /*loginUser = new LoginUser();
        loginUser.setSid("51ee8e133cbf018e1343dd5f9e788fd0");
        loginUser.setName("张三三");
        taskDO.setSource(0);
        taskDO.setCiCodePath("888_888_080_888");*/

        //需要将上一个状态的 isNewest 改为 1
        dataAuditHistoryMapper.updateIsNewestByDataIds(dataIds);
        //批量新增审核状态
        dataAuditHistoryMapper.insertBatch(returnDataAuditHistoryList(dataIds, reqVO.getTaskSid(), auditStatus, loginUser.getName()));

        //更新es里面文档数据的状态
        collectionMapper.updateDataStatus(index, dataIds, auditStatus, null, null);
    }

    @Transactional
    @Override
    public void passCollectDataAll(TaskDataInfoQueryReq reqVO) {
        CollectionTaskDO taskDO = collectionTaskMapper.selectById(reqVO.getTaskSid());
        if (null == taskDO){
            throw new ServiceException(ServerErrorEnum.TASK_NOT_EXISTS);
        }
        String index = collectionMapper.getEsIndex(taskDO.getSource(), taskDO.getCiCodePath());
        //String index = CollectionMapper.getEsIndex(taskDO.getSource(), taskDO.getCiCodePath());
        Set<String> dataIds = collectionMapper.selectTaskDataIdsByQuery(reqVO, index);

        if (CollUtil.isEmpty(dataIds)){
            throw new ServiceException(ServerErrorEnum.DATA_NOT_EXISTS_AWAIT_TWO_AUDIT);
        }

        int auditStatus = AuditStatusEnum.ALREADY_AUDIT_PASS.getCode();
        LoginUser loginUser = SecurityUtils.getLoginUser();
        //TODO: 测试参数
        /*loginUser = new LoginUser();
        loginUser.setSid("51ee8e133cbf018e1343dd5f9e788fd0");
        loginUser.setName("张三三");
        taskDO.setSource(0);
        taskDO.setCiCodePath("888_888_080_888");*/

        //需要将上一个状态的 isNewest 改为 1
        dataAuditHistoryMapper.updateIsNewestByDataIds(dataIds);
        //批量新增审核状态
        dataAuditHistoryMapper.insertBatch(returnDataAuditHistoryList(dataIds, reqVO.getTaskSid(), auditStatus, loginUser.getName()));

        //更新es里面文档数据的状态
        collectionMapper.updateDataStatus(index, dataIds, auditStatus, null, loginUser.getName());
    }

    @Transactional
    @Override
    public void removeCollectDataAll(TaskDataInfoQueryReq reqVO) {
        CollectionTaskDO taskDO = collectionTaskMapper.selectById(reqVO.getTaskSid());
        if (null == taskDO){
            throw new ServiceException(ServerErrorEnum.TASK_NOT_EXISTS);
        }
        String index = collectionMapper.getEsIndex(taskDO.getSource(), taskDO.getCiCodePath());
        //String index = CollectionMapper.getEsIndex(taskDO.getSource(), taskDO.getCiCodePath());
        Set<String> dataIds = collectionMapper.selectTaskDataIdsByQuery(reqVO, index);

        if (CollUtil.isEmpty(dataIds)){
            throw new ServiceException(ServerErrorEnum.DATA_NOT_EXISTS_AWAIT_DELETE);
        }

        dataAuditHistoryMapper.deleteByDataIds(dataIds);
        //LoginUser loginUser = SecurityUtils.getLoginUser();
        //TODO: 测试参数
        /*loginUser = new LoginUser();
        loginUser.setSid("51ee8e133cbf018e1343dd5f9e788fd0");
        loginUser.setName("张三三");
        taskDO.setSource(0);
        taskDO.setCiCodePath("888_888_080_888");*/
        //删除es里面文档数据
        collectionMapper.deleteDataByIds(index, dataIds);
    }

    @Transactional
    @Override
    public void twoAuditCollectData(TwoAuditCollectDataReq reqVO) {
        if (dataAuditHistoryMapper.selectNotAuditStatusDataCount(reqVO.getDataId(), AuditStatusEnum.ALREADY_DISPATCH.getCode()) > 0){
            throw new ServiceException(ServerErrorEnum.DATA_NOT_AWAIT_TWO_AUDIT);
        }
        CollectionTaskDO taskDO = collectionTaskMapper.selectById(reqVO.getTaskSid());
        if (null == taskDO){
            throw new ServiceException(ServerErrorEnum.TASK_NOT_EXISTS);
        }

        String index = collectionMapper.getEsIndex(taskDO.getSource(), taskDO.getCiCodePath());
        LoginUser loginUser = SecurityUtils.getLoginUser();
        //TODO: 测试参数
        /*loginUser = new LoginUser();
        loginUser.setSid("51ee8e133cbf018e1343dd5f9e788fd0");
        loginUser.setName("张三三");
        taskDO.setSource(0);
        taskDO.setCiCodePath("888_888_080_888");*/

        Set<String> dataIds = new HashSet<>(1);
        dataIds.add(reqVO.getDataId());
        int oldAuditStatus = reqVO.getAuditStatus();
        //需要将上一个状态的 isNewest 改为 1
        dataAuditHistoryMapper.updateIsNewestByDataIds(dataIds);
        //批量新增审核状态
        DataAuditHistoryDO dataAuditHistoryDO = new DataAuditHistoryDO();
        dataAuditHistoryDO.setSid(UUIDUtils.getUUID());
        dataAuditHistoryDO.setDataId(reqVO.getDataId());
        dataAuditHistoryDO.setTaskSid(reqVO.getTaskSid());
        dataAuditHistoryDO.setCreatorName(loginUser.getName());
        int auditStatus = 0;
        if (oldAuditStatus == AuditStatusEnum.ALREADY_AUDITOR_REJECT.getCode()){
            //打回
            auditStatus = AuditStatusEnum.ALREADY_AUDITOR_REJECT.getCode();
        } else if (oldAuditStatus == AuditStatusEnum.ALREADY_AUDIT_PASS.getCode()) {
            //通过
            auditStatus = AuditStatusEnum.ALREADY_AUDIT_PASS.getCode();
        }
        dataAuditHistoryDO.setAuditStatus(auditStatus);
        dataAuditHistoryMapper.insert(dataAuditHistoryDO);

        //更新es里面文档数据的状态
        //String index = CollectionMapper.getEsIndex(taskDO.getSource(), taskDO.getCiCodePath());
        Map<String, Object> data = reqVO.getData();
        if (CollUtil.isEmpty(data)){
            data = new HashMap<>();
        }
        data.put(EsFieldConstant.AUDIT_STATUS, auditStatus);
        data.put(EsFieldConstant.TWO_AUDITOR, loginUser.getName());
        collectionMapper.updateDataById(index, reqVO.getDataId(), reqVO.getData());
    }


    private void taskDisposeUtil(List<TaskDisposeRsp> list) {
        if (CollUtil.isNotEmpty(list)) {
            for (TaskDisposeRsp req : list) {
                req.setPendCount(dataAuditHistoryMapper.selectTaskAwaitDisposeCountByUserSid(req.getSid()));
                //测试参数
                /*req.setSid("51ee89d955aa0189d955b81bb7e8dec4");
                req.setSource(0);
                req.setCiCodePath("888_888_080_888"); //local_col_888_888080888 000_001_002_001


                String index = CollectionMapper.getEsIndex(req.getSource(), req.getCiCodePath());
                if (StringUtils.isNotEmpty(index)){
                    req.setPendCount(collectionMapper.selectTaskDataCountByStatus(index, req.getSid(), AuditStatusEnum.ALREADY_DISPATCH.getCode()));
                }*/
            }
        }
    }

    /**
     * 返回数据审核历史集合
     * @param dataIds
     * @param taskSid
     * @param auditStatus
     * @param userName
     * @return
     */
    private List<DataAuditHistoryDO> returnDataAuditHistoryList(Set<String> dataIds, String taskSid, int auditStatus, String userName) {
        List<DataAuditHistoryDO> list = new ArrayList<>();
        for (String dataId : dataIds) {
            DataAuditHistoryDO dataAuditHistoryDO = new DataAuditHistoryDO();
            dataAuditHistoryDO.setSid(UUIDUtils.getUUID());
            dataAuditHistoryDO.setDataId(dataId);
            dataAuditHistoryDO.setTaskSid(taskSid);
            dataAuditHistoryDO.setAuditStatus(auditStatus);
            dataAuditHistoryDO.setCreatorName(userName);
            list.add(dataAuditHistoryDO);
        }
        return list;
    }
}
