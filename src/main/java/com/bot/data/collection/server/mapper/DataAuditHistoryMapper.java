package com.bot.data.collection.server.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bot.data.collection.common.mybatis.mapper.BaseMapperX;
import com.bot.data.collection.common.mybatis.query.LambdaQueryWrapperX;
import com.bot.data.collection.server.constants.AuditStatusEnum;
import com.bot.data.collection.server.model.entity.DataAuditHistoryDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

@Mapper
public interface DataAuditHistoryMapper extends BaseMapperX<DataAuditHistoryDO> {

    /**
     * 查询不满足审核状态的数据
     * @param dataId
     * @return
     */
    default Long selectNotAuditStatusDataCount(String dataId, Integer auditStatus) {
        LambdaQueryWrapper<DataAuditHistoryDO> wrapper = new LambdaQueryWrapperX<DataAuditHistoryDO>()
                .eqIfPresent(DataAuditHistoryDO::getDataId, dataId)
                .eqIfPresent(DataAuditHistoryDO::getIsNewest, 0)
                .neIfPresent(DataAuditHistoryDO::getAuditStatus, auditStatus);
        return selectCount(wrapper);
    }

    /*default Long selectNotFirstAuditStatusDataCount(String dataId) {
        LambdaQueryWrapper<DataAuditHistoryDO> wrapper = new LambdaQueryWrapper<DataAuditHistoryDO>()
                .eq(DataAuditHistoryDO::getDataId, dataId)
                .eq(DataAuditHistoryDO::getIsNewest, 0)
                .ne(DataAuditHistoryDO::getAuditStatus, AuditStatusEnum.ALREADY_DISPATCH)
                .ne(DataAuditHistoryDO::getAuditStatus, AuditStatusEnum.ALREADY_AUDITOR_REJECT);
                //.and(q -> q.eq(DataAuditHistoryDO::getAuditStatus, auditStatus).or().isNull(DataAuditHistoryDO::getAuditStatus))
                //.neIfPresent(DataAuditHistoryDO::getAuditStatus, auditStatus);
        return selectCount(wrapper);
    }*/
    Long selectNotFirstAuditStatusDataCount(@Param("dataId") String dataId);

    /**
     * 指与该用户相关的任务中待处理的数据总条数
     * @param userSid
     * @return
     */
    Long selectAwaitDisposeCountByUserSid(@Param("userSid") String userSid);
    /**
     * 指该任务用户相关的任务中待处理的数据总条数
     * @param userSid
     * @param taskSid
     * @return
     */
    Long selectTaskAwaitDisposeCountByUserSid(@Param("taskSid") String taskSid);
    /**
     * 指该用户已处理的数据总条数
     * @param userSid
     * @return
     */
    Long selectAlreadyDisposeCountByUserSid(@Param("userSid") String userSid);

    /**
     * 指该任务该用户已处理的数据总条数
     * @param userSid
     * @param taskSid
     * @return
     */
    Long selectTaskAlreadyDisposeCountByUserSid(@Param("userSid") String userSid, @Param("taskSid") String taskSid);
    /**
     * 指该用户今日已处理的数据条数
     * @param userSid
     * @return
     */
    Long selectTodayDisposeCountByUserSid(@Param("userSid") String userSid, @Param("todayTime") LocalDateTime todayTime);
    /**
     * 指用户提交后被打回的次数
     * @param userSid
     * @return
     */
    Long selectRejectCountByUserSid(@Param("userSid") String userSid);

    /**
     * 查询不能派发的数据条数（状态不满足的）
     * @param dataIds
     * @return
     */
    Long selectNotDispatchDataCount(@Param("dataIds") Set<String> dataIds);
    /**
     * 查询不能打回的数据条数（状态不满足的）
     * @param dataIds
     * @return
     */
    Long selectNotRejectDataCount(@Param("dataIds") Set<String> dataIds);
    /**
     * 查询不能通过的数据条数（状态不满足的）
     * @param dataIds
     * @return
     */
    Long selectNotPassDataCount(@Param("dataIds") Set<String> dataIds);

    Long selectAwaitTwoAuditDataCount(@Param("dataIds") Set<String> dataIds);


    int updateIsNewestByDataIds(@Param("dataIds") Set<String> dataIds);

    int deleteByDataIds(@Param("dataIds") Set<String> dataIds);

    /**
     * 出阿信历史集合
     * @param dataId
     * @return
     */
    default List<DataAuditHistoryDO> selectHistoryList(String dataId) {
        return selectList(new LambdaQueryWrapperX<DataAuditHistoryDO>().eq(DataAuditHistoryDO::getDataId, dataId)
                .orderByAsc(DataAuditHistoryDO::getCreateTime)
        );
    }
}
