package com.bot.data.collection.server.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bot.data.collection.common.pojo.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 采集任务
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("collection_task")
public class CollectionTaskDO extends BaseDO {
    @TableId(value = "sid")
    private String sid;
    //名称
    @TableField(value = "sno")
    private String sno;
    @TableField(value = "`name`")
    private String name;
    //说明
    @TableField(value = "remark")
    private String remark;
    @TableField(value = "ci_sid")
    private String ciSid;
    //任务分类
    @TableField(value = "ci_names")
    private String ciNames;
    //数据子集分类
    @TableField(value = "ci_code_path")
    private String ciCodePath;

    //项目sid
    @TableField(value = "pi_sid")
    private String piSid;
    //项目编号
    @TableField(value = "project")
    private String project;
    //项目类型 0-基础数据 1-业务数据 2-转出数据
   /* @TableField(value = "pi_type")
    private Integer piType;*/

    //采集类型 0-长期（含历史） 1-长期（指定时间开始） 2-长期（持续更新）
    @TableField(value = "cycle_type")
    private Integer cycleType;
    //获取方式 0-app 1-web
    @TableField(value = "`from`")
    private Integer from;
    //获取来源 0-本地脚本 1-三方接口（清博）
    @TableField(value = "source")
    private Integer source;
    //获取来源 页面获取路径
    @TableField(value = "page_path")
    private String pagePath;
    //采集连接或app包名
    @TableField(value = "link")
    private String link;
    //操作人id
    @TableField(value = "operator_sid")
    private String operatorSid;
    //创建时间
    /*@TableField(value = "ct_create_time")
    private LocalDateTime ctCreateTime;*/
    //采集开始时间
    @TableField(value = "start_time")
    private LocalDateTime startTime;
    //采集结束时间
    @TableField(value = "end_time")
    private LocalDateTime endTime;
    //采集状态 -1 -删除标志 0 -停止采集 1 -待采集 2 -采集中 3 -采集结束 4 -暂停采集
    //采集状态：0 -采集中 1 -采集结束
    @TableField(value = "status")
    private Integer status;
    //更新时间
    /*@TableField(value = "ct_update_time", insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private LocalDateTime ctUpdateTime;*/
    //分类处理
    @TableField(value = "need_format_library")
    private Boolean needFormatLibrary;
    //扩展信息
    @TableField(value = "ext")
    private String ext;
    @TableField(value = "task_status")
    private Integer taskStatus;
    //初始新增时间
    @TableField(value = "initial_add_time")
    private LocalDateTime initialAddTime;
    //最新新增时间
    @TableField(value = "newest_add_time")
    private LocalDateTime newestAddTime;
    //创建人name
    @TableField(value = "creator_name")
    private String creatorName;
    //更新人name
    @TableField(value = "updater_name")
    private String updaterName;
}
