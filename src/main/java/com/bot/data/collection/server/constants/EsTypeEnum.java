package com.bot.data.collection.server.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum EsTypeEnum {
    KEYWORD(0,"keyword"),    //关键字 (全匹配查询)
    TEXT(1,"text"),       //文本（分词模糊查询）
    DATE_DEFAULT(2,"date"),         //日期（yyyy-MM-dd HH:mm:ss）
    OBJECT(3,"object"),     //拓展字段（对象）
    UNKNOWN(-99,"");

    private final Integer code;

    private final String value;


    public static Integer getCode(String value){
        return Stream.of(values()).filter(b -> b.value.equals(value)).findFirst().orElse(UNKNOWN).getCode();
    }
    public static String getValue(Integer code){
        return Stream.of(values()).filter(b -> b.code.equals(code)).findFirst().orElse(UNKNOWN).getValue();
    }
}
