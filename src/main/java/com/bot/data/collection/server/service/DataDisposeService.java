package com.bot.data.collection.server.service;

import com.bot.data.collection.common.pojo.PageResult;
import com.bot.data.collection.server.model.dto.CollectionFlatDTO;
import com.bot.data.collection.server.model.req.*;
import com.bot.data.collection.server.model.rsp.DataInfoRsp;
import com.bot.data.collection.server.model.rsp.TaskDisposeRsp;
import com.bot.data.collection.server.model.rsp.TaskSummarizeRsp;
import com.bot.data.collection.server.model.rsp.UserDataCountRsp;

import java.util.List;
import java.util.Map;

public interface DataDisposeService {


    PageResult<TaskDisposeRsp> getTaskDisposePage(TaskDisposePageReq reqVO);

    UserDataCountRsp getUserDataCount();


    /**
     * 初审
     * @param taskSid
     * @return
     */
    DataInfoRsp getDataInfo(String taskSid);

    /**
     * 复审
     * @param dataId
     * @param taskSid
     * @return
     */
    DataInfoRsp getDataInfo(String dataId, String taskSid);

    /**
     * 派发采集数据（批量）
     * @param reqVO
     */
    void dispatchCollectDataBatch(CollectDataReq reqVO);

    /**
     * 打回采集数据（批量）
     * @param reqVO
     */
    void rejectCollectDataBatch(CollectDataReq reqVO);

    void passCollectDataBatch(CollectDataReq reqVO);

    void removeCollectDataBatch(CollectDataReq reqVO);

    void auditCollectData(DataFirstAuditReq reqVO);

    void dispatchCollectDataAll(TaskDataInfoQueryReq reqVO);

    void rejectCollectDataAll(TaskDataInfoQueryReq reqVO);

    void passCollectDataAll(TaskDataInfoQueryReq reqVO);

    void removeCollectDataAll(TaskDataInfoQueryReq reqVO);

    void twoAuditCollectData(TwoAuditCollectDataReq reqVO);
}
