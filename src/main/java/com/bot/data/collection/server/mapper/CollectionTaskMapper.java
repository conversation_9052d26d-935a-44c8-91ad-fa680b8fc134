package com.bot.data.collection.server.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bot.data.collection.common.mybatis.mapper.BaseMapperX;
import com.bot.data.collection.common.mybatis.query.LambdaQueryWrapperX;
import com.bot.data.collection.common.pojo.PageResult;
import com.bot.data.collection.server.constants.CollectionStatusEnum;
import com.bot.data.collection.server.model.dto.TaskPageReqDTO;
import com.bot.data.collection.server.model.entity.CollectionTaskDO;
import com.bot.data.collection.server.model.req.CollectionTaskPageReq;
import com.bot.data.collection.server.model.req.TaskDisposePageReq;
import com.bot.data.collection.server.model.req.TaskSummarizePageReq;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CollectionTaskMapper extends BaseMapperX<CollectionTaskDO> {

    //List<CollectionTaskRsp> selectCollectionTaskPage(@Param("page") Page<CollectionTaskRsp> page, @Param("reqVO") CollectionTaskPageReq reqVO);

    List<CollectionTaskDO> selectTaskPage(@Param("page") Page<CollectionTaskDO> page, @Param("reqVO") TaskPageReqDTO reqVO);

    default PageResult<CollectionTaskDO> selectPage(CollectionTaskPageReq request) {
        return selectPage(request, new LambdaQueryWrapperX<CollectionTaskDO>()
                .likeIfPresent(CollectionTaskDO::getSno, request.getSno())
                .likeIfPresent(CollectionTaskDO::getName, request.getName())
                .likeIfPresent(CollectionTaskDO::getCiNames, request.getCiNames())
                .likeIfPresent(CollectionTaskDO::getProject, request.getProject())
                .likeIfPresent(CollectionTaskDO::getLink, request.getLink())
                .eqIfPresent(CollectionTaskDO::getFrom, request.getFrom())
                .eqIfPresent(CollectionTaskDO::getSource, request.getSource())
                .eqIfPresent(CollectionTaskDO::getCycleType, request.getCycleType())
                .eqIfPresent(CollectionTaskDO::getStatus, request.getStatus())

                .betweenIfPresent(CollectionTaskDO::getCreateTime, request.getStartTime(), request.getEndTime())
                .orderByDesc(CollectionTaskDO::getUpdateTime));
    }
    default Long selectCountByCiSid(String ciSid) {
        return selectCount(new LambdaQueryWrapperX<CollectionTaskDO>()
                .eqIfPresent(CollectionTaskDO::getCiSid, ciSid)
                .neIfPresent(CollectionTaskDO::getStatus, CollectionStatusEnum.ENDED.getCode()));
    }

    default Long selectCountByPiSid(String piSid) {
        return selectCount(new LambdaQueryWrapperX<CollectionTaskDO>()
                .eqIfPresent(CollectionTaskDO::getPiSid, piSid)
                .neIfPresent(CollectionTaskDO::getStatus, CollectionStatusEnum.ENDED.getCode()));
    }

    default int deleteByPiSid(String piSid) {
        return delete(new LambdaQueryWrapperX<CollectionTaskDO>().eq(CollectionTaskDO::getPiSid, piSid));
    }

    default PageResult<CollectionTaskDO> selectPage(TaskSummarizePageReq request) {
        return selectPage(request, new LambdaQueryWrapperX<CollectionTaskDO>()
                .likeIfPresent(CollectionTaskDO::getSno, request.getSno())
                .likeIfPresent(CollectionTaskDO::getName, request.getName())
                .likeIfPresent(CollectionTaskDO::getCiNames, request.getCiNames())
                .likeIfPresent(CollectionTaskDO::getProject, request.getProject())
                /*.likeIfPresent(CollectionTaskDO::getLink, request.getLink())
                .eqIfPresent(CollectionTaskDO::getFrom, request.getFrom())
                .eqIfPresent(CollectionTaskDO::getSource, request.getSource())
                .eqIfPresent(CollectionTaskDO::getCycleType, request.getCycleType())
                .eqIfPresent(CollectionTaskDO::getStatus, request.getStatus())

                .betweenIfPresent(CollectionTaskDO::getCreateTime, request.getStartTime(), request.getEndTime())*/
                .orderByDesc(CollectionTaskDO::getUpdateTime));
    }

    default PageResult<CollectionTaskDO> selectPage(TaskDisposePageReq request) {
        return selectPage(request, new LambdaQueryWrapperX<CollectionTaskDO>()
                .likeIfPresent(CollectionTaskDO::getSno, request.getSno())
                .likeIfPresent(CollectionTaskDO::getName, request.getName())
                .likeIfPresent(CollectionTaskDO::getCiNames, request.getCiNames())
                .likeIfPresent(CollectionTaskDO::getProject, request.getProject())
                /*.likeIfPresent(CollectionTaskDO::getLink, request.getLink())
                .eqIfPresent(CollectionTaskDO::getFrom, request.getFrom())
                .eqIfPresent(CollectionTaskDO::getSource, request.getSource())
                .eqIfPresent(CollectionTaskDO::getCycleType, request.getCycleType())
                .eqIfPresent(CollectionTaskDO::getStatus, request.getStatus())

                .betweenIfPresent(CollectionTaskDO::getCreateTime, request.getStartTime(), request.getEndTime())*/
                .orderByDesc(CollectionTaskDO::getUpdateTime));
    }
}
