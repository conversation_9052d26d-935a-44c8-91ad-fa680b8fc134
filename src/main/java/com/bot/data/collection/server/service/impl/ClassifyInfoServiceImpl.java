package com.bot.data.collection.server.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.bot.data.collection.common.exception.ServiceException;
import com.bot.data.collection.common.utils.RedisUtils;
import com.bot.data.collection.common.utils.UUIDUtils;
import com.bot.data.collection.server.constants.RedisConstant;
import com.bot.data.collection.server.constants.ServerErrorEnum;
import com.bot.data.collection.server.convert.ClassConvert;
import com.bot.data.collection.server.mapper.ClassifyInfoMapper;
import com.bot.data.collection.server.mapper.CollectionTaskMapper;
import com.bot.data.collection.server.model.dto.ClassDTO;
import com.bot.data.collection.server.model.dto.UserLockDTO;
import com.bot.data.collection.server.model.entity.ClassifyInfoDO;
import com.bot.data.collection.server.model.req.ClassifyCreateReq;
import com.bot.data.collection.server.model.req.ClassifyUpdateReq;
import com.bot.data.collection.server.service.ClassifyInfoService;
import com.bot.data.collection.system.convert.UserConvert;
import com.bot.data.collection.system.utils.LoginUser;
import com.bot.data.collection.system.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class ClassifyInfoServiceImpl implements ClassifyInfoService {
    @Resource
    ClassifyInfoMapper classifyInfoMapper;
    @Resource
    CollectionTaskMapper collectionTaskMapper;
    @Resource
    RedisUtils redisUtils;



    /**
     * 添加分类对象
     * @param classDTOList 当前层数所有的分类对象
     * @param classifyInfoDO 待放入的分类对象
     * @return
     */
    public boolean addClassDTO(List<ClassDTO> classDTOList, ClassifyInfoDO classifyInfoDO) {
        boolean flag = false;
        for (ClassDTO dto : classDTOList) {
            if (dto.getSid().equals(classifyInfoDO.getParentSid())) {
                //判断当前循环的对象sid 为 入参对象的父sid
                List<ClassDTO> cList = dto.getClassDTOList();
                if (CollectionUtils.isEmpty(cList)){
                    cList = new ArrayList<>();
                }
                ClassDTO classDTO = ClassConvert.INSTANCE.convert(classifyInfoDO);
                cList.add(classDTO);
                dto.setClassDTOList(cList);
                flag = true;
                break;
            }else {
                if (null != dto.getClassDTOList()){
                    //递归调用
                    flag = addClassDTO(dto.getClassDTOList(), classifyInfoDO);
                    if (flag){
                        break;
                    }
                }
            }
        }
        return flag;
    }

    @Override
    public List<ClassDTO> getClassifyLibraryList() {
        //查询一级分类
        List<ClassifyInfoDO> classifyInfos = classifyInfoMapper.selectByParentSidIsNull();
        if (CollectionUtils.isNotEmpty(classifyInfos)){
            List<ClassDTO> classDTOList = ClassConvert.INSTANCE.convert(classifyInfos);
            List<ClassifyInfoDO> classifyInfoDOList = classifyInfoMapper.selectByParentSidNotIsNull();
            if (CollectionUtils.isNotEmpty(classifyInfoDOList)){
                classifyInfoDOList.forEach(o -> {
                    addClassDTO(classDTOList, o);
                });
            }
            return classDTOList;
        }
        return null;
    }

    @Override
    public Boolean getClassifyLockAdd() {
        String value = redisUtils.get(RedisConstant.CLASSIFY_LOCK_ADD_USER);
        if (StringUtils.isNotEmpty(value)){
            return true;
        }
        return false;
    }

    @Override
    public String createClassifyInfo(ClassifyCreateReq reqVO) {
        validateLock();

        String parentSid = reqVO.getParentSid();
        validateClassifyNameUnique(null, reqVO.getName(), parentSid);
        ClassifyInfoDO parentClassifyInfo = classifyInfoMapper.selectById(parentSid);
        if (parentClassifyInfo == null){
            throw new ServiceException(ServerErrorEnum.PRENT_CLASSIFY_NOT_EXISTS);
        }
        String sid = UUIDUtils.getUUID();
        String codePath = parentClassifyInfo.getCodePath();
        ClassifyInfoDO classifyInfo = new ClassifyInfoDO();
        classifyInfo.setSid(sid);
        String code = "";
        if (StringUtils.isEmpty(parentSid)){
            //新增一级分类（注：二级分类有父id）
            classifyInfo.setParentSid("");
            code = String.format("%03d", classifyInfoMapper.getMaxCodeByparentSidIsNull() + 1);
            codePath = code;
        }else {
            classifyInfo.setParentSid(parentSid);
            code = String.format("%03d", classifyInfoMapper.getMaxCodeByParentSid(parentSid) + 1);
            codePath = codePath + "_" + code;
        }
        classifyInfo.setCode(code);
        classifyInfo.setCodePath(codePath);
        classifyInfo.setName(reqVO.getName());
        classifyInfoMapper.insert(classifyInfo);
        return sid;
    }

    /**
     * 校验是否被锁定
     */
    private void validateLock() {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        String operatorSid = loginUser.getSid();
        //如果没锁定，为当前人员锁定
        if (!redisUtils.exists(RedisConstant.CLASSIFY_LOCK_ADD_USER)){
            //如果没锁定，锁定当前登陆人，且可新增
            redisUtils.set(RedisConstant.CLASSIFY_LOCK_ADD_USER, JSON.toJSONString(UserConvert.INSTANCE.convertUserLockDTO(loginUser)));
        }else {
            //如果锁定，判断为当前人员锁定
            String value = redisUtils.get(RedisConstant.CLASSIFY_LOCK_ADD_USER);
            UserLockDTO lockUser = StringUtils.isNotEmpty(value) ? JSON.parseObject(value, UserLockDTO.class) : null;
            String lockUserSid = (null != lockUser) ? lockUser.getSid() : null;
            if (!operatorSid.equals(lockUserSid)){
                throw new ServiceException(ServerErrorEnum.LOCK_FAIL.getCode(), "操作失败，已被用户["+lockUser.getName()+"]锁定");
            }
        }
    }


    @Override
    public void updateClassifyInfo(ClassifyUpdateReq reqVO) {
        validateLock();
        String parentSid = reqVO.getParentSid();
        String sid = reqVO.getSid();

        ClassifyInfoDO classifyInfo = classifyInfoMapper.selectById(sid);
        if (null == classifyInfo){
            throw new ServiceException(ServerErrorEnum.DATA_INFO_NOT_EXISTS);
        }

        //TODO:子分类关联任务情况
        //校验：分类已与任务进行关联：编辑分类时既不支持更改上级分类，也不支持更改分类名称。
        validateTaskExists(sid);

        validateClassifyNameUnique(sid, reqVO.getName(), parentSid);
        classifyInfo.setName(reqVO.getName());
        classifyInfoMapper.updateById(classifyInfo);
    }

    @Override
    public void updateClassifyLock(Boolean lock) {
        String value = redisUtils.get(RedisConstant.CLASSIFY_LOCK_ADD_USER);
        if (lock){
            //上锁
            if (StringUtils.isEmpty(value)){
                //成功上锁
                LoginUser loginUser = SecurityUtils.getLoginUser();
                redisUtils.set(RedisConstant.CLASSIFY_LOCK_ADD_USER, JSON.toJSONString(UserConvert.INSTANCE.convertUserLockDTO(loginUser)));
            }else {
                UserLockDTO lockUser = JSON.parseObject(value, UserLockDTO.class);
                if (SecurityUtils.getLoginUser().getSid().equals(lockUser.getSid())){
                    //重复上锁
                    throw new ServiceException(ServerErrorEnum.LOCK_FAIL_REPEAT);
                }else {
                    //已被xxx锁定
                    throw new ServiceException(ServerErrorEnum.LOCK_FAIL.getCode(), ServerErrorEnum.LOCK_FAIL.getMessage()+"，已被用户["+lockUser.getName()+"]锁定");
                }
            }
        }else {
            //解锁
            if (StringUtils.isEmpty(value)){
                //未上锁
                throw new ServiceException(ServerErrorEnum.UNLOCK_FAIL_INEXISTENCE);
            }else {
                UserLockDTO lockUser = JSON.parseObject(value, UserLockDTO.class);
                if (SecurityUtils.getLoginUser().getSid().equals(lockUser.getSid())){
                    //成功解锁
                    redisUtils.remove(RedisConstant.CLASSIFY_LOCK_ADD_USER);
                }else {
                    //已被xxx锁定，被哪个用户锁定了这个功能
                    throw new ServiceException(ServerErrorEnum.UNLOCK_FAIL.getCode(), ServerErrorEnum.UNLOCK_FAIL.getMessage()+"，已被用户["+lockUser.getName()+"]锁定");
                }
            }
        }
    }

    /*@Transactional
    @Override
    public void importClassifyData() {
        List<ClassifyInfoDO> l1 = classifyInfoMapper.selectList();

        List<ClassDTO> classDTOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(l1)){
            for (ClassifyInfoDO classifyInfo : l1) {
                ClassDTO dto = new ClassDTO();
                dto.setSid(classifyInfo.getSid());
                dto.setCode(classifyInfo.getCode());
                dto.setName(classifyInfo.getName());
                dto.setCodePath(classifyInfo.getCodePath());
                classDTOList.add(dto);
            }
        }
        List<LibraryInfo> libraryInfos = libraryInfoMapper.selectAll();
        if (CollectionUtils.isNotEmpty(libraryInfos)){
            libraryInfos.forEach(o -> {
                addClassDTO(classDTOList, o);
            });
        }
        for (ClassDTO dto : classDTOList) {
            insert(dto, false, null, null);
            *//*System.out.println("dto = " + dto);
            ClassifyInfoDO classifyInfo = new ClassifyInfoDO();
            String parentSid = reqVO.getParentSid();
            validateClassifyNameUnique(null, reqVO.getName(), parentSid);
            String sid = UUIDUtils.getUUID();
            String codePath = reqVO.getParentCodePath();
            ClassifyInfoDO classifyInfo = new ClassifyInfoDO();
            classifyInfo.setSid(sid);
            String code = "";
            if (StringUtils.isEmpty(parentSid)){
                //新增一级分类（注：二级分类有父id）
                classifyInfo.setParentSid("");
                code = String.format("%03d", classifyInfoMapper.getMaxCodeByparentSidIsNull() + 1);
                codePath = code;
            }else {
                classifyInfo.setParentSid(parentSid);
                code = String.format("%03d", classifyInfoMapper.getMaxCodeByParentSid(parentSid) + 1);
                codePath = codePath + "_" + code;
            }
            classifyInfo.setCode(code);
            classifyInfo.setCodePath(codePath);
            classifyInfo.setName(reqVO.getName());
            classifyInfoMapper.insert(classifyInfo);*//*
        }*/
        /*
        *
        * String parentSid = reqVO.getParentSid();
        validateClassifyNameUnique(null, reqVO.getName(), parentSid);
        String sid = UUIDUtils.getUUID();
        String codePath = reqVO.getParentCodePath();
        ClassifyInfoDO classifyInfo = new ClassifyInfoDO();
        classifyInfo.setSid(sid);
        String code = "";
        if (StringUtils.isEmpty(parentSid)){
            //新增一级分类（注：二级分类有父id）
            classifyInfo.setParentSid("");
            code = String.format("%03d", classifyInfoMapper.getMaxCodeByparentSidIsNull() + 1);
            codePath = code;
        }else {
            classifyInfo.setParentSid(parentSid);
            code = String.format("%03d", classifyInfoMapper.getMaxCodeByParentSid(parentSid) + 1);
            codePath = codePath + "_" + code;
        }
        classifyInfo.setCode(code);
        classifyInfo.setCodePath(codePath);
        classifyInfo.setName(reqVO.getName());
        classifyInfoMapper.insert(classifyInfo);

    }*/

    private void insert(ClassDTO classDTO, boolean isAdd, String parentSid, String codePath) {
        System.out.println("dto = " + classDTO);
        if (isAdd){
            ClassifyInfoDO classifyInfo = new ClassifyInfoDO();
            classifyInfo.setSid(classDTO.getSid());

            String code = "";
            classifyInfo.setParentSid(parentSid);
            code = String.format("%03d", classifyInfoMapper.getMaxCodeByParentSid(parentSid) + 1);
            codePath = codePath + "_" + code;

            classifyInfo.setCode(code);
            classifyInfo.setCodePath(codePath);
            classifyInfo.setName(classDTO.getName());
            classifyInfoMapper.insert(classifyInfo);
        }




        if (CollUtil.isNotEmpty(classDTO.getClassDTOList())){
            for (ClassDTO dto : classDTO.getClassDTOList()) {
                insert(dto, true, classDTO.getSid(), classDTO.getCodePath());
            }
        }

    }


    /**
     * 添加分类对象
     * @param classDTOList 当前层数所有的分类对象
     * @param libraryInfo 待放入的分类对象
     * @return
     */
    /*public boolean addClassDTO(List<ClassDTO> classDTOList, LibraryInfoDO libraryInfo) {
        boolean flag = false;
        for (ClassDTO dto : classDTOList) {
            //第二层
            if (StringUtils.isEmpty(libraryInfo.getLiParentSid())){
                //判断父sid是否为空
                if (dto.getSid().equals(libraryInfo.getLiCiSid())){
                    //判断当前循环的对象sid 为 入参对象的父sid
                    List<ClassDTO> cList = dto.getClassDTOList();
                    if (CollectionUtils.isEmpty(cList)){
                        cList = new ArrayList<>();
                    }
                    ClassDTO classDTO = ClassConvert.INSTANCE.convertClassDTO(libraryInfo);
                    classDTO.setCodePath(dto.getCodePath() + "_" + libraryInfo.getLiCode());
                    cList.add(classDTO);
                    dto.setClassDTOList(cList);
                    flag = true;
                    break;
                }
            }else {
                if (dto.getSid().equals(libraryInfo.getLiParentSid())){
                    //判断当前循环的对象sid 为 入参对象的父sid
                    List<ClassDTO> cList = dto.getClassDTOList();
                    if (CollectionUtils.isEmpty(cList)){
                        cList = new ArrayList<>();
                    }
                    ClassDTO classDTO = ClassConvert.INSTANCE.convertClassDTO(libraryInfo);
                    classDTO.setCodePath(dto.getCodePath() + "_" + libraryInfo.getLiCode());
                    cList.add(classDTO);
                    dto.setClassDTOList(cList);
                    flag = true;
                    break;
                }else {
                    if (null != dto.getClassDTOList()){
                        //递归调用
                        flag = addClassDTO(dto.getClassDTOList(), libraryInfo);
                        if (flag){
                            break;
                        }
                    }
                }

            }

        }
        return flag;
    }*/


    /**
     * 校验多级分类名唯一
     * @param sid
     * @param name
     */
    void validateClassifyNameUnique(String sid, String name, String parentSid) {
        if (classifyInfoMapper.selectCountByLiName(sid, name, parentSid) > 0) {
            throw new ServiceException(ServerErrorEnum.CLASSIFY_NAME_EXISTS);
        }
    }

    /**
     * 校验：分类已与任务进行关联：编辑分类时既不支持更改上级分类，也不支持更改分类名称。
     * @param sid
     */
    private void validateTaskExists(String sid){
        if (collectionTaskMapper.selectCountByCiSid(sid) > 0){
            throw new ServiceException(ServerErrorEnum.CLASSIFY_CORRELATION_TASK);
        }
    }

    /**
     * 添加分类对象
     * @param classDTOList 当前层数所有的分类对象
     * @param libraryInfo 待放入的分类对象
     * @return
     */
    /*public boolean addClassDTO(List<ClassDTO> classDTOList, LibraryInfo libraryInfo) {
        boolean flag = false;
        for (ClassDTO dto : classDTOList) {
            //第二层
            if (StringUtils.isEmpty(libraryInfo.getLiParentSid())){
                //判断父sid是否为空
                if (dto.getSid().equals(libraryInfo.getLiCiSid())){
                    //判断当前循环的对象sid 为 入参对象的父sid
                    List<ClassDTO> cList = dto.getClassDTOList();
                    if (CollectionUtils.isEmpty(cList)){
                        cList = new ArrayList<>();
                    }
                    ClassDTO classDTO = ClassConvert.INSTANCE.convertClassDTO(libraryInfo);
                    classDTO.setCodePath(dto.getCodePath() + "_" + libraryInfo.getLiCode());
                    cList.add(classDTO);
                    dto.setClassDTOList(cList);
                    flag = true;
                    break;
                }
            }else {
                if (dto.getSid().equals(libraryInfo.getLiParentSid())){
                    //判断当前循环的对象sid 为 入参对象的父sid
                    List<ClassDTO> cList = dto.getClassDTOList();
                    if (CollectionUtils.isEmpty(cList)){
                        cList = new ArrayList<>();
                    }
                    ClassDTO classDTO = ClassConvert.INSTANCE.convertClassDTO(libraryInfo);
                    classDTO.setCodePath(dto.getCodePath() + "_" + libraryInfo.getLiCode());
                    cList.add(classDTO);
                    dto.setClassDTOList(cList);
                    flag = true;
                    break;
                }else {
                    if (null != dto.getClassDTOList()){
                        //递归调用
                        flag = addClassDTO(dto.getClassDTOList(), libraryInfo);
                        if (flag){
                            break;
                        }
                    }
                }

            }

        }
        return flag;
    }*/
}
