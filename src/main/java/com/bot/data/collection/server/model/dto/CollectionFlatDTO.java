package com.bot.data.collection.server.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel(value="采集结构信息对象",description="采集结构信息对象")
@Data
public class CollectionFlatDTO {

    @ApiModelProperty(value="张三",name="字段中文名")
    private String cname;

    @ApiModelProperty(value="zhangsan",name="字段英文名",required = true)
    private String ename;

    @ApiModelProperty(value="0-关键字 (全匹配查询) 1-文本（分词模糊查询） 2-日期（yyyy-MM-dd HH:mm:ss） 3-可拓展字段",name="字段类型",required = true)
    private Integer type;

    @ApiModelProperty(name="模板结构")
    private List<CollectionFlatDTO> flat;

    /*@Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        CollectionFlatDTO other = (CollectionFlatDTO) obj;
        return type.equals(other.type) && EName.equals(other.EName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(type, EName);
    }*/

}
