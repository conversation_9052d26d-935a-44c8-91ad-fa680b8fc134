package com.bot.data.collection.server.mapper;

import com.bot.data.collection.common.mybatis.mapper.BaseMapperX;
import com.bot.data.collection.common.mybatis.query.LambdaQueryWrapperX;
import com.bot.data.collection.server.model.entity.CollectionFlatTemplateDO;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface CollectionFlatTemplateMapper extends BaseMapperX<CollectionFlatTemplateDO> {

    default Long selectCountByCftName(String sid, String name) {
        return selectCount(new LambdaQueryWrapperX<CollectionFlatTemplateDO>()
                .eqIfPresent(CollectionFlatTemplateDO::getCftName, name)
                .neIfPresent(CollectionFlatTemplateDO::getSid, sid));
    }

}
