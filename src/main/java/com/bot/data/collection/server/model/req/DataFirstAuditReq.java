package com.bot.data.collection.server.model.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;


@ApiModel(value="复审提交数据对象",description="复审提交数据对象")
@Data
public class DataFirstAuditReq {



    @ApiModelProperty(value="111",name="数据Id")
    @NotEmpty(message = "数据Id不能为空")
    private String dataId;

    @ApiModelProperty(value="111",name="任务sid")
    @NotEmpty(message = "任务sid不能为空")
    private String taskSid;

    /*@ApiModelProperty(value="001-值",name="任务数据")
    private Map<String, Object> data;*/

}
