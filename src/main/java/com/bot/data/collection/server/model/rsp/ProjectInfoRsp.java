package com.bot.data.collection.server.model.rsp;

import com.bot.data.collection.common.utils.DateUtils;
import com.bot.data.collection.server.model.base.ProjectInfoBaseVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@ApiModel(value="项目信息对象",description="项目信息")
@Data
public class ProjectInfoRsp extends ProjectInfoBaseVO {

    @ApiModelProperty(value="11111",name="项目sid",required=true)
    private String sid;

    @ApiModelProperty(value="TS-111111",name="项目编号",required=true)
    private String piSno;

    @ApiModelProperty(value="采集数据结构",name="采集数据结构")
    private String piMapper;

    @ApiModelProperty(value="结构字典",name="结构字典")
    private String piMapperDict;

    @ApiModelProperty(value="zhangsan",name="创建人名称")
    private String creatorName;

    @ApiModelProperty(value = "2022-07-01 00:00:00", name = "创建时间", required = true)
    @JsonFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "2022-07-01 00:00:00", name = "更新时间", required = true)
    @JsonFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime updateTime;
}
