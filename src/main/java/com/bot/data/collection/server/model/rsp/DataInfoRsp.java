package com.bot.data.collection.server.model.rsp;

import com.bot.data.collection.server.model.dto.CollectionFlatDTO;
import com.bot.data.collection.server.model.dto.DataAuditHistoryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@ApiModel(value="任务数据信息对象",description="任务数据信息对象")
@Data
public class DataInfoRsp {

    @ApiModelProperty(value="001-值",name="任务数据")
    private Map<String, Object> data;

    @ApiModelProperty(value="xxx",name="数据结构")
    private List<CollectionFlatDTO> flatDTOS;

    @ApiModelProperty(value="xxx",name="数据审核历史")
    private List<DataAuditHistoryDTO> auditHistoryDTOS;

    @ApiModelProperty(value="111",name="此任务待处理条数")
    private Long awaitDisposeCount;

    @ApiModelProperty(value="111",name="此任务已处理条数")
    private Long alreadyDisposeCount;
}
