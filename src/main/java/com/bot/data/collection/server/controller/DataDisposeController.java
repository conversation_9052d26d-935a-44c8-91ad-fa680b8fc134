package com.bot.data.collection.server.controller;

import com.bot.data.collection.common.exception.ServiceException;
import com.bot.data.collection.common.pojo.CommonResult;
import com.bot.data.collection.common.pojo.PageResult;
import com.bot.data.collection.server.model.req.DataFirstAuditReq;
import com.bot.data.collection.server.model.req.TaskDisposePageReq;
import com.bot.data.collection.server.model.req.TwoAuditCollectDataReq;
import com.bot.data.collection.server.model.rsp.DataInfoRsp;
import com.bot.data.collection.server.model.rsp.TaskDisposeRsp;
import com.bot.data.collection.server.model.rsp.UserDataCountRsp;
import com.bot.data.collection.server.service.DataDisposeService;
import com.bot.data.collection.system.constants.SystemErrorEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;

@Api(tags = "数据处理管理")
@RestController
@RequestMapping("/data-dispose")
@Validated
public class DataDisposeController {
    @Resource
    DataDisposeService dataDisposeService;


    @PostMapping("/task-page")
    @ApiOperation(value = "获得采集任务处理分页列表")
    //@PermitAll
    //@PreAuthorize("@ss.hasPermission('system:user:list')")
    public CommonResult<PageResult<TaskDisposeRsp>> getTaskDisposePage(@Valid @RequestBody TaskDisposePageReq reqVO) {
        return CommonResult.success(dataDisposeService.getTaskDisposePage(reqVO));
    }

    @GetMapping("/user-count")
    @ApiOperation(value = "获得用户数据统计信息")
    //@PermitAll
    //@PreAuthorize("@ss.hasPermission('system:user:list')")
    public CommonResult<UserDataCountRsp> getUserDataCount() {
        return CommonResult.success(dataDisposeService.getUserDataCount());
    }

    @GetMapping("/get-first-audit-info")
    @ApiOperation(value = "获得任务数据信息详情（初审）")
    //@PermitAll
    //@PreAuthorize("@ss.hasPermission('system:user:query')")
    public CommonResult<DataInfoRsp> getDataInfo(@ApiParam(name = "taskSid", value = "123", required = true) @RequestParam("taskSid") String taskSid) {
        if (StringUtils.isBlank(taskSid)) {
            throw new ServiceException(SystemErrorEnum.PARAM_IS_NULL);
        }
        return CommonResult.success(dataDisposeService.getDataInfo(taskSid));
    }

    @GetMapping("/get-two-audit-info")
    @ApiOperation(value = "获得任务数据信息详情（复审）")
    //@PermitAll
    //@PreAuthorize("@ss.hasPermission('system:user:query')")
    public CommonResult<DataInfoRsp> getDataInfo(@ApiParam(name = "dataId", value = "123", required = true) @RequestParam("dataId") String dataId, @ApiParam(name = "taskSid", value = "123", required = true) @RequestParam("taskSid") String taskSid) {
        if (StringUtils.isBlank(dataId) || StringUtils.isBlank(taskSid)) {
            throw new ServiceException(SystemErrorEnum.PARAM_IS_NULL);
        }
        return CommonResult.success(dataDisposeService.getDataInfo(dataId, taskSid));
    }

    @PostMapping("/audit")
    @ApiOperation(value = "审核采集数据（初审）")
    //@PermitAll
    public CommonResult<Boolean> auditCollectData(@Valid @RequestBody DataFirstAuditReq reqVO) {
        dataDisposeService.auditCollectData(reqVO);
        return CommonResult.success(true);
    }

    @PostMapping("/two-audit")
    @ApiOperation(value = "审核采集数据（复审）")
    //@PermitAll
    public CommonResult<Boolean> twoAuditCollectData(@Valid @RequestBody TwoAuditCollectDataReq reqVO) {
        dataDisposeService.twoAuditCollectData(reqVO);
        return CommonResult.success(true);
    }
}
