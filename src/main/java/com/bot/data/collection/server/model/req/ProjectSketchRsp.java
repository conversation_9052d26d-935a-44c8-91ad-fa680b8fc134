package com.bot.data.collection.server.model.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@ApiModel(value="项目概述信息对象",description="项目信息")
@Data
public class ProjectSketchRsp {
    @ApiModelProperty(value="11111",name="项目sid",required=true)
    private String sid;

    @ApiModelProperty(value="TS-111111",name="项目编号",required=true)
    private String piSno;

    @ApiModelProperty(name="关联项目名称/编号")
    private String ctProject;
}
