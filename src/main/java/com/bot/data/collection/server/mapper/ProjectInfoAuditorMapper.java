package com.bot.data.collection.server.mapper;

import com.bot.data.collection.common.mybatis.mapper.BaseMapperX;
import com.bot.data.collection.common.mybatis.query.LambdaQueryWrapperX;
import com.bot.data.collection.server.model.entity.ProjectInfoAuditorDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ProjectInfoAuditorMapper extends BaseMapperX<ProjectInfoAuditorDO> {


    default List<ProjectInfoAuditorDO> selectListByProjectSid(String projectSid) {
        return selectList(new LambdaQueryWrapperX<ProjectInfoAuditorDO>()
                .eqIfPresent(ProjectInfoAuditorDO::getProjectInfoSid, projectSid));
    }

    /**
     * 删除
     * @param projectSid 项目sid
     * @param type 审核人类型
     * @param flagNum 标记是用户还是用户组
     */
    default void deleteInfoByProjectSid(String projectSid, Integer type, int flagNum) {
        LambdaQueryWrapperX<ProjectInfoAuditorDO> wrapper = new LambdaQueryWrapperX<ProjectInfoAuditorDO>()
                .eqIfPresent(ProjectInfoAuditorDO::getProjectInfoSid, projectSid)
                .eqIfPresent(ProjectInfoAuditorDO::getType, type);
        if(flagNum == 1){
            wrapper.isNull(ProjectInfoAuditorDO::getUserGroupSid);
        }else if (flagNum == 2){
            wrapper.isNull(ProjectInfoAuditorDO::getUserSid);
        }
        delete(wrapper);
    }

    default void deleteInfoByProjectSid(String projectSid) {
        delete(new LambdaQueryWrapperX<ProjectInfoAuditorDO>()
                .eqIfPresent(ProjectInfoAuditorDO::getProjectInfoSid, projectSid));
    }

    default void deleteInfoByUserSid(String userSid) {
        delete(new LambdaQueryWrapperX<ProjectInfoAuditorDO>()
                .eqIfPresent(ProjectInfoAuditorDO::getUserSid, userSid));
    }
    default void deleteInfoByUserGroupSid(String userGroupSid) {
        delete(new LambdaQueryWrapperX<ProjectInfoAuditorDO>()
                .eqIfPresent(ProjectInfoAuditorDO::getUserGroupSid, userGroupSid));
    }

    int deleteBatchByFirstAuditorUserSids(@Param("userSids") List<String> userSids, @Param("projectSid") String projectSid, @Param("updater") String updater);

    int deleteBatchByFirstAuditorUserGroupSids(@Param("userGroupSids") List<String> userGroupSids, @Param("projectSid") String projectSid, @Param("updater") String updater);

    int deleteBatchByTwoAuditorUserSids(@Param("userSids") List<String> userSids, @Param("projectSid") String projectSid, @Param("updater") String updater);

    int deleteBatchByTwoAuditorUserGroupSids(@Param("userGroupSids") List<String> userGroupSids, @Param("projectSid") String projectSid, @Param("updater") String updater);
}
