package com.bot.data.collection.server.model.req;

import com.bot.data.collection.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@ApiModel(value="项目信息对象",description="项目信息")
@Data
public class ProjectPageReq extends PageParam {

    @ApiModelProperty(value="TS-111111",name="项目编号")
    private String piSno;

    @ApiModelProperty(value="信息",name="项目名称")
    private String piName;

    @ApiModelProperty(value="0-内部数据 1-业务数据 2-转出数据",name="项目类型")
    private Integer piType;

    @ApiModelProperty(value = "[2022-07-01 00:00:00, 2022-07-01 23:59:59]", name = "创建时间")
    //@DateTimeFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;


}
