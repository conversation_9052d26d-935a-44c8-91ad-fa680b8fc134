package com.bot.data.collection.server.convert;

import com.bot.data.collection.server.model.dto.ClassDTO;
import com.bot.data.collection.server.model.entity.ClassifyInfoDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ClassConvert {
    ClassConvert INSTANCE = Mappers.getMapper(ClassConvert.class);

    //Page<ProjectInfoRsp> convertPage(Page<ProjectInfo> page);

    ClassDTO convert(ClassifyInfoDO classifyInfo);
    List<ClassDTO> convert(List<ClassifyInfoDO> classifyInfos);
    /*@Mappings({
            @Mapping(target = "code", source = "ciCode"),
            @Mapping(target = "name", source = "ciName"),
            @Mapping(target = "codePath", source = "ciCode")
    })
    ClassDTO convertClassDTO(ClassifyInfoDO obj);*/

    /*@Mappings({
            @Mapping(target = "code", source = "liCode"),
            @Mapping(target = "name", source = "liName")
            *//*,
            @Mapping(target = "name", source = "liName"),
            @Mapping(target = "name", source = "liName")*//*

    })
    ClassDTO convertClassDTO(LibraryInfoDO obj);*/
    //List<ClassDTO> convertClassDTO(List<LibraryInfo> classifyInfos);

    /*@Mappings({
            @Mapping(target = "code", source = "liCode"),
            @Mapping(target = "name", source = "liName")
            *//*,
            @Mapping(target = "name", source = "liName"),
            @Mapping(target = "name", source = "liName")*//*

    })
    ClassDTO convertClassDTO(LibraryInfo obj);*/
}
