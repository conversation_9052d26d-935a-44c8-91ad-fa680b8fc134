package com.bot.data.collection.server.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bot.data.collection.common.mybatis.mapper.BaseMapperX;
import com.bot.data.collection.common.mybatis.query.LambdaQueryWrapperX;
import com.bot.data.collection.server.model.entity.ClassifyInfoDO;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;


import java.util.List;

@Mapper
public interface ClassifyInfoMapper extends BaseMapperX<ClassifyInfoDO> {
    default List<ClassifyInfoDO> selectAll(){
        return selectList(new LambdaQueryWrapper<ClassifyInfoDO>());
    }
    @Select("SELECT sid,code,name,parent_sid,code_path,create_time FROM classify_info WHERE (parent_sid is null or parent_sid = '') and deleted = 0;")
    List<ClassifyInfoDO> selectByParentSidIsNull();
    @Select("SELECT sid,code,name,parent_sid,code_path,create_time FROM classify_info WHERE parent_sid is not null and parent_sid != '' and deleted = 0;")
     List<ClassifyInfoDO> selectByParentSidNotIsNull();

    default ClassifyInfoDO selectByName(String name){
        return selectOne(new LambdaQueryWrapper<ClassifyInfoDO>().eq(ClassifyInfoDO::getName, name));
    }

    default ClassifyInfoDO selectByCodePath(String codePath){
        return selectOne(new LambdaQueryWrapper<ClassifyInfoDO>().eq(ClassifyInfoDO::getCodePath, codePath));
    }




    default Long selectCountByLiName(String sid, String name, String parentSid) {
        LambdaQueryWrapper<ClassifyInfoDO> wrapper = new LambdaQueryWrapperX<ClassifyInfoDO>()
                .eqIfPresent(ClassifyInfoDO::getName, name)
                .neIfPresent(ClassifyInfoDO::getSid, sid);
        if (StringUtils.isNotEmpty(parentSid)){
            wrapper.eq(ClassifyInfoDO::getParentSid, parentSid);
        }else {
            wrapper.isNull(ClassifyInfoDO::getParentSid);
        }
        return selectCount(wrapper);
    }


    @Select("<script>"
            + "select ifnull(max(code),0) from classify_info where parent_sid = #{parentSid}"
            + "</script>")
    int getMaxCodeByParentSid(@Param("parentSid") String parentSid);
    @Select("<script>"
            + "select ifnull(max(code),0) from classify_info where parent_sid = '' or parent_sid is null"
            + "</script>")
    int getMaxCodeByparentSidIsNull();


}
