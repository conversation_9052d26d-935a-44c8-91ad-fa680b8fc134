package com.bot.data.collection.server.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bot.data.collection.common.pojo.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 采集任务
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("data_audit_history")
public class DataAuditHistoryDO extends BaseDO {
    @TableId(value = "sid")
    private String sid;
    //名称
    @TableField(value = "task_sid")
    private String taskSid;
    @TableField(value = "`data_id`")
    private String dataId;

    //审核状态：0-待派发，1-已派发，2-待复审，3-已打回，4-已通过
    @TableField(value = "audit_status")
    private Integer auditStatus;
    /**
     * 是否最新：false
     */
    @TableField(value = "is_newest")
    private Boolean isNewest;
    //创建人name
    @TableField(value = "creator_name")
    private String creatorName;
    //最终初审人sid
    /*@TableField(value = "final_first_auditor")
    private String finalFirstAuditor;*/
}
