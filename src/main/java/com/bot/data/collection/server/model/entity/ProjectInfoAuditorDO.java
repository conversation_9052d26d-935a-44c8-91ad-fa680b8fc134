package com.bot.data.collection.server.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bot.data.collection.common.pojo.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 项目审核人关系表
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("project_info_auditor")
public class ProjectInfoAuditorDO extends BaseDO {
    @TableField(value = "sid")
    private String sid;

    //项目信息ID
    @TableField(value = "project_info_sid")
    private String projectInfoSid;

    //用户ID
    @TableField(value = "user_sid")
    private String userSid;

    //用户组ID
    @TableField(value = "user_group_sid")
    private String userGroupSid;

    //审核人类型：0-初审人，1-复审人
    @TableField(value = "type")
    private Integer type;
}
