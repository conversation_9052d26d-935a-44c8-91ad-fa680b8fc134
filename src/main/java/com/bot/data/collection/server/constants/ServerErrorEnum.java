package com.bot.data.collection.server.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ServerErrorEnum {

    DATA_INFO_NOT_EXISTS(600000, "数据信息不存在"),
    PROJECT_INFO_NOT_EXISTS(600000, "项目信息不存在"),
    PROJECT_INFO_EXISTS(600001, "项目信息已经存在"),
    PROJECT_FIRST_AUDITOR_NOT_EXISTS(600002, "项目初审人不能为空"),
    PROJECT_TWO_AUDITOR_NOT_EXISTS(600003, "项目复审人不能为空"),
    PROJECT_TASK_IS_EXISTS(600004, "项目已关联任务"),

    COLLECTION_FLAT_TEMPLATE_NAME_EXISTS(600101, "采集结构模板已经存在"),
    FIELD_TYPE_NOT_NULL(600102, "字段类型不能为空"),
    FIELD_ENNAME_NOT_NULL(600103, "字段英文名不能为空"),
    FIELD_ENNAME_NOT_REPEAT(600104, "字段英文名不能重复"),

    CLASSIFY_NAME_EXISTS(600201, "分类名称已经存在"),
    CLASSIFY_CORRELATION_TASK(600202, "分类已与任务进行关联"),
    PRENT_CLASSIFY_NOT_EXISTS(600203, "父分类不存在"),


    LOCK_FAIL(602001, "上锁失败"),
    UNLOCK_FAIL(602002, "解锁失败"),
    LOCK_FAIL_REPEAT(602003, "不能重复上锁"),
    UNLOCK_FAIL_INEXISTENCE(602004, "锁不存在"),

    TASK_ALREADY_RUNNING(603001, "任务已执行，无法进行更新和删除"),
    TASK_NOT_EXISTS(603002, "任务不存在"),

    DATA_NOT_AWAIT_DISPATCH(604001, "派发失败，已选数据需为待派发状态"),
    DATA_NOT_ALREADY_DISPATCH(604002, "派发失败，已选数据需为已派发状态"),
    DATA_NOT_AWAIT_TWO_AUDIT(604003, "派发失败，已选数据需为待复审状态"),

    DATA_NOT_EXISTS_AWAIT_DISPATCH(604001, "派发失败，不存在待派发的数据"),
    //DATA_NOT_EXISTS_ALREADY_DISPATCH(604002, "派发失败，已选数据需为已派发状态"),
    DATA_NOT_EXISTS_AWAIT_TWO_AUDIT(604003, "打回或通过操作失败，不存在待复审的数据"),
    DATA_NOT_EXISTS_AWAIT_DELETE(604004, "删除失败，数据数据不存在"),

    // 用户信息错误------------------------------------------------------
    USER_USERNAME_EXISTS(300005, "用户账号已经存在"),
    USER_MOBILE_EXISTS(300006, "手机号已经存在"),
    USER_EMAIL_EXISTS(300007, "邮箱已经存在"),
    USER_NOT_EXISTS(300008, "用户不存在"),
    USER_IMPORT_LIST_IS_EMPTY(300009, "导入用户数据不能为空！"),
    USER_PASSWORD_FAILED(300010, "用户密码校验失败"),
    USER_IS_DISABLE(300011, "名字为【{}】的用户已被禁用"),
    USER_USERNAME_NOT_NULL(300012, "用户账号不能为空"),
    USER_NAME_PASS_ERROR(300013, "用户名密码错误"),
    USER_STATE_ERROR(300014, "用户已禁用，请与系统管理员联系"),
    USER_MOBILE_FORMAT_ERR(300015, "手机号格式错误"),
    USER_OLD_PASS_ERROR(300016, "用户老密码错误"),
    USER_PASS_FORMAT_ERROR(300017, "至少8个字符，且必须包括数字、大写字母、小写字母和符号"),

    // ========== 用户组 ==========
    USER_GROUP_NOT_EXISTS(300100, "用户组不存在"),
    USER_GROUP_NAME_EXISTS(300101, "用户组名称已经存在"),

    // ========== 角色 ==========
    ROLE_NOT_EXISTS(300200, "角色不存在"),
    ROLE_NAME_EXISTS(300201, "角色名称已经存在"),
    ROLE_CODE_EXISTS(300202, "角色编码已经存在"),
    ROLE_CAN_NOT_UPDATE_SYSTEM_TYPE_ROLE(300203, "不能操作类型为系统内置的角色"),
    ROLE_RELATION_USER(300204, "角色已与用户关联，将无法删除"),
    ROLE_RELATION_USER_GROUP(300204, "角色已与用户组关联，将无法删除"),

    // ========== 菜单权限 ==========
    MENU_NAME_DUPLICATE(300300, "已经存在该名字的菜单"),
    MENU_PARENT_NOT_EXISTS(300301, "父菜单不存在"),
    MENU_PARENT_ERROR(300302, "不能设置自己为父菜单"),
    MENU_NOT_EXISTS(300303, "菜单不存在"),
    MENU_EXISTS_CHILDREN(300304, "存在子菜单，无法删除"),
    MENU_PARENT_NOT_DIR_OR_MENU(300305, "父菜单的类型必须是目录或者菜单"),


    // 系统级错误
    MISSING_PARAM_ERROR(400000, "参数缺失"),
    PARAM_VALUE_ERROR(400001, "参数值不匹配"),
    PARAM_IS_NULL(400002, "参数值为空"),
    UN_LOGGED(400100, "未登录"),
    UN_AUTHENTICATED(400200, "无权限"),

    // 数据解析错误
    GET_DATA_ERROR(500100, "数据解析错误"),
    DATA_BLANK_ERROR(500101, "必填字段缺失"),
    SIGNATURE_ERROR(500102, "签名命错误"),
    DATA_FORMAT_ERROR(500103, "数据格式错误"),
    REQUEST_TIME_EXCEEDED(500104, "请求时间超限"),


    ;

    /*
ErrorCode  = new ErrorCode(, "");
    ErrorCode  = new ErrorCode(, "");
    ErrorCode  = new ErrorCode(, "");
    ErrorCode  = new ErrorCode(, "");
    ErrorCode  = new ErrorCode(, "");
    ErrorCode  = new ErrorCode(, "");
    ErrorCode  = new ErrorCode(, "");
    ErrorCode  = new ErrorCode(, "");

        // ========== AUTH 模块 1-002-000-000 ==========
    ErrorCode AUTH_THIRD_LOGIN_NOT_BIND = new ErrorCode(1_002_000_005, "未绑定账号，需要进行绑定");
    ErrorCode AUTH_MOBILE_NOT_EXISTS = new ErrorCode(1_002_000_007, "手机号不存在");

    GlobalErrorEnum
    ErrorCode BAD_REQUEST = new ErrorCode(400, "请求参数不正确");
    ErrorCode UNAUTHORIZED = new ErrorCode(401, "");
    ErrorCode  = new ErrorCode(, "");
    ErrorCode  = new ErrorCode(404, "");
    ErrorCode METHOD_NOT_ALLOWED = new ErrorCode(405, "请求方法不正确");
    ErrorCode LOCKED = new ErrorCode(423, "请求失败，请稍后重试"); // 并发请求，不允许
    ErrorCode TOO_MANY_REQUESTS = new ErrorCode(429, "请求过于频繁，请稍后重试");

    // ========== 服务端错误段 ==========

    ErrorCode INTERNAL_SERVER_ERROR = new ErrorCode(500, "系统异常");
    ErrorCode NOT_IMPLEMENTED = new ErrorCode(501, "功能未实现/未开启");

    // ========== 自定义错误段 ==========
    ErrorCode REPEATED_REQUESTS = new ErrorCode(900, "重复请求，请稍后重试"); // 重复请求
    ErrorCode DEMO_DENY = new ErrorCode(901, "演示模式，禁止写操作");

    ErrorCode UNKNOWN = new ErrorCode(999, "未知错误");*/
    private final Integer code;
    private final String message;
}
