package com.bot.data.collection.system.model.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bot.data.collection.common.pojo.BaseDO;
import lombok.*;

/**
 * 用户和角色关联
 *
 * <AUTHOR>
 */
@TableName("system_user_role")
@Builder
@Data
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class UserRoleDO extends BaseDO {

    /**
     * 自增主键
     */
    @TableId
    private String sid;
    /**
     * 用户组 ID
     */
    private String userSid;
    /**
     * 角色 ID
     */
    private String roleSid;

}
