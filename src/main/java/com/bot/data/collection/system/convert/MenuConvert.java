package com.bot.data.collection.system.convert;

import com.bot.data.collection.system.model.entity.MenuDO;
import com.bot.data.collection.system.model.req.MenuCreateReq;
import com.bot.data.collection.system.model.req.MenuUpdateReq;
import com.bot.data.collection.system.model.rsp.MenuResq;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
@Mapper
public interface MenuConvert {
    MenuConvert INSTANCE = Mappers.getMapper(MenuConvert.class);

    MenuDO convert(MenuCreateReq reqVO);

    MenuDO convert(MenuUpdateReq reqVO);

    MenuResq convert(MenuDO reqVO);
}
