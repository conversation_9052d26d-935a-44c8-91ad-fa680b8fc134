package com.bot.data.collection.system.model.req;

import com.bot.data.collection.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;

@ApiModel("角色用户组分页 Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class RoleUserGroupPageReq extends PageParam {

    @ApiModelProperty(value = "zhangsan", name = "用户组名称（模糊匹配）")
    private String userGroupName;

    @ApiModelProperty(value = "1024", name = "用户组id", required = true)
    @NotEmpty(message = "用户组sid不能为空")
    private String roleSid;
}
