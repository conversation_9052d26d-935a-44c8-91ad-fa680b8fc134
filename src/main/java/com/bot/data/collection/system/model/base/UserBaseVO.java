package com.bot.data.collection.system.model.base;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;

/**
 * 用户 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class UserBaseVO {

    @ApiModelProperty(value = "江南七怪", name = "用户账号", required = true)
    @NotBlank(message = "用户名不能为空")
    //@Pattern(regexp = "^[a-zA-Z0-9]{4,30}$", message = "用户账号由 数字、字母 组成")
    @Size(max = 30, message = "用户名长度不能超过 30 个字符")
    private String username;

    @ApiModelProperty(value = "18888888888", name = "手机号")
    @NotBlank(message = "手机号不能为空")
    private String mobile;

    @ApiModelProperty(value = "@iocoder.cn", name = "用户邮箱")
    @Email(message = "邮箱格式不正确")
    @Size(max = 50, message = "邮箱长度不能超过 50 个字符")
    private String email;

    @ApiModelProperty(value = "江南", name = "地址")
    private String address;

    @ApiModelProperty(value = "999999", name = "邮编")
    private String zipCode;

    @ApiModelProperty(value = "我是一个用户", name = "备注")
    private String remark;

    @ApiModelProperty(value = "0", name = "帐号状态（0正常 1停用）")
    private Integer status;


}
