package com.bot.data.collection.system.controller;

import com.bot.data.collection.common.pojo.CommonResult;
import com.bot.data.collection.common.pojo.PageResult;
import com.bot.data.collection.system.constants.SystemErrorEnum;
import com.bot.data.collection.common.exception.ServiceException;
import com.bot.data.collection.system.model.dto.UserGroupInfoDTO;
import com.bot.data.collection.system.model.rsp.UserGroupResp;
import com.bot.data.collection.system.service.UserGroupService;
import com.bot.data.collection.system.model.req.UserGroupCreateReq;
import com.bot.data.collection.system.model.req.UserGroupUpdateReq;

import com.bot.data.collection.system.model.req.UserGroupPageReq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@Api(tags = "用户组管理")
@RestController
@RequestMapping("/userGroup")
@Validated
public class UserGroupController {

    @Resource
    UserGroupService userGroupService;

    @PostMapping("/create")
    @ApiOperation(value = "新增用户组")
    //@PreAuthorize("@ss.hasPermission('system:user:create')")
    public CommonResult<String> createUserGroup(@Valid @RequestBody UserGroupCreateReq reqVO) {
        return CommonResult.success(userGroupService.createUserGroup(reqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改用户组")
    //@PreAuthorize("@ss.hasPermission('system:user:update')")
    public CommonResult<Boolean> updateUserGroup(@Valid @RequestBody UserGroupUpdateReq reqVO) {
        userGroupService.updateUserGroup(reqVO);
        return CommonResult.success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除用户组")
    //@PreAuthorize("@ss.hasPermission('system:user:delete')")
    public CommonResult<Boolean> deleteUserGroup(@ApiParam(name = "sid", value = "123", required = true) @RequestParam("sid") String sid) {
        if (StringUtils.isBlank(sid)) {
            throw new ServiceException(SystemErrorEnum.PARAM_IS_NULL);
        }
        userGroupService.deleteUserGroup(sid);
        return CommonResult.success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得用户组详情")
    //@PermitAll
    //@PreAuthorize("@ss.hasPermission('system:user:query')")
    public CommonResult<UserGroupResp> getUserGroup(@ApiParam(name = "sid", value = "123", required = true) @RequestParam("sid") String sid) {
        if (StringUtils.isBlank(sid)) {
            throw new ServiceException(SystemErrorEnum.PARAM_IS_NULL);
        }
        return CommonResult.success(userGroupService.getUserGroup(sid));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得用户组分页列表")
    //@PermitAll
    //@PreAuthorize("@ss.hasPermission('system:user:list')")
    public CommonResult<PageResult<UserGroupResp>> getUserGroupPage(@Valid @RequestBody UserGroupPageReq reqVO) {
        // 获得用户分页列表
        PageResult<UserGroupResp> pageResult = userGroupService.getUserGroupPage(reqVO);
        return CommonResult.success(pageResult);
    }

    @GetMapping("/list")
    @ApiOperation(value = "获得用户组信息集合")
    //@PermitAll
    //@PreAuthorize("@ss.hasPermission('system:user:query')")
    public CommonResult<List<UserGroupInfoDTO>> getUserList() {
        return CommonResult.success(userGroupService.getUserGroupInfoList());
    }



}
