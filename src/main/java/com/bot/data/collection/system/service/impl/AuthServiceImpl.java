package com.bot.data.collection.system.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.bot.data.collection.common.utils.Des3Utils;
import com.bot.data.collection.common.utils.RedisUtils;
import com.bot.data.collection.system.convert.AuthConvert;
import com.bot.data.collection.system.model.entity.OAuth2AccessTokenDO;
import com.bot.data.collection.system.model.entity.UserDO;
import com.bot.data.collection.common.constants.CommonStatusEnum;
import com.bot.data.collection.system.constants.SystemErrorEnum;
import com.bot.data.collection.common.exception.ServiceException;
import com.bot.data.collection.system.model.req.AuthLoginReq;
import com.bot.data.collection.system.model.rsp.AuthLoginResp;
import com.bot.data.collection.system.service.AuthService;
import com.bot.data.collection.system.service.OAuth2TokenService;
import com.bot.data.collection.system.service.UserService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

@Service
@Slf4j
@AllArgsConstructor
public class AuthServiceImpl implements AuthService {
    @Resource
    private UserService userService;
    @Resource
    private OAuth2TokenService oauth2TokenService;

    private final RedisUtils redisUtils;
    @Override
    public AuthLoginResp login(AuthLoginReq reqVO) {
        // 校验验证码
        validateCaptcha(reqVO);

        // 使用账号密码，进行登录
        UserDO user = authenticate(reqVO.getMobile(), reqVO.getPassword());

        // 创建 Token 令牌，记录登录日志
        return createTokenAfterLoginSuccess(user.getSid());
    }

    private void validateCaptcha(AuthLoginReq reqVO) {
        String verCode =  redisUtils.get(Des3Utils.encode(reqVO.getVerKey()));
        if (Objects.isNull(verCode)) {
            throw new ServiceException(SystemErrorEnum.CODE_FAILURE);
        }
        if (Integer.valueOf(reqVO.getVerCode()) != Integer.valueOf(JSONObject.parseObject(verCode,String.class))) {
            throw new ServiceException(SystemErrorEnum.CODE_ERROR);
        }
    }

    public UserDO authenticate(String mobile, String password) {
        //final LoginLogTypeEnum logTypeEnum = LoginLogTypeEnum.LOGIN_USERNAME;
        // 校验账号是否存在
        UserDO user = userService.getUserByMobile(mobile);
        if (user == null) {
            throw new ServiceException(SystemErrorEnum.USER_NOT_EXISTS);
        }
        /*if (user == null) {
            //创建登录日志
            createLoginLog(null, username, logTypeEnum, LoginResultEnum.BAD_CREDENTIALS);
            throw exception(AUTH_LOGIN_BAD_CREDENTIALS);
        }*/
        if (!userService.isPasswordMatch(password, user.getPassword())) {
            //创建登录日志
            //createLoginLog(user.getId(), username, logTypeEnum, LoginResultEnum.BAD_CREDENTIALS);
            throw new ServiceException(SystemErrorEnum.AUTH_LOGIN_BAD_CREDENTIALS);
        }
        // 校验是否禁用
        if (ObjectUtil.notEqual(user.getStatus(), CommonStatusEnum.EFFECTIVE.getCode())) {
            //createLoginLog(user.getId(), username, logTypeEnum, LoginResultEnum.USER_DISABLED);
            throw new ServiceException(SystemErrorEnum.AUTH_LOGIN_USER_DISABLED);
        }
        return user;
    }

    @Override
    public void logout(String token, Integer logType) {
        // 删除访问令牌
        OAuth2AccessTokenDO accessTokenDO = oauth2TokenService.removeAccessToken(token);
        if (accessTokenDO == null) {
            return;
        }
        // 删除成功，则记录登出日志
        //createLogoutLog(accessTokenDO.getUserId(), accessTokenDO.getUserType(), logType);
    }

    @Override
    public AuthLoginResp refreshToken(String refreshToken) {
        OAuth2AccessTokenDO accessTokenDO = oauth2TokenService.refreshAccessToken(refreshToken);
        return AuthConvert.INSTANCE.convert(accessTokenDO);
    }


    /**
     * 创建 Token 令牌
     * @param userSid
     * @return
     */
    private AuthLoginResp createTokenAfterLoginSuccess(String userSid) {
        // 插入登陆日志
        //createLoginLog(userId, username, logType, LoginResultEnum.SUCCESS);
        // 创建访问令牌
        OAuth2AccessTokenDO accessTokenDO = oauth2TokenService.createAccessToken(userSid);
        // 构建返回结果
        return AuthConvert.INSTANCE.convert(accessTokenDO);
    }
}
