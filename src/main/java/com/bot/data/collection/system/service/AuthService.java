package com.bot.data.collection.system.service;

import com.bot.data.collection.system.model.entity.UserDO;
import com.bot.data.collection.system.model.req.AuthLoginReq;
import com.bot.data.collection.system.model.rsp.AuthLoginResp;

public interface AuthService {
    /**
     * 账号登录
     *
     * @param reqVO 登录信息
     * @return 登录结果
     */
    AuthLoginResp login(AuthLoginReq reqVO);



    /**
     * 验证账号 + 密码。如果通过，则返回用户
     *
     * @param username 账号
     * @param password 密码
     * @return 用户
     */
    UserDO authenticate(String username, String password);

    /**
     * 基于 token 退出登录
     *
     * @param token token
     * @param logType 登出类型
     */
    void logout(String token, Integer logType);

    /**
     * 刷新访问令牌
     *
     * @param refreshToken 刷新令牌
     * @return 登录结果
     */
    AuthLoginResp refreshToken(String refreshToken);
}
