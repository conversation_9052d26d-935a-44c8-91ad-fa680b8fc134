package com.bot.data.collection.system.model.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;

@ApiModel("用户密码更新 Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserPasswordUpdateReq {

    @ApiModelProperty(value = "111aaaBBB", name = "旧密码", required = true)
    @NotEmpty(message = "旧密码不能为空")
    private String oldPassword;

    @ApiModelProperty(value = "111aaaBBB###", name = "新密码", required = true)
    @NotEmpty(message = "新密码不能为空")
    private String newPassword;


}
