package com.bot.data.collection.system.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bot.data.collection.system.model.entity.OAuth2RefreshTokenDO;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface OAuth2RefreshTokenMapper extends BaseMapper<OAuth2RefreshTokenDO> {
    default int deleteByRefreshToken(String refreshToken) {
        return delete(new LambdaQueryWrapper<OAuth2RefreshTokenDO>().eq(OAuth2RefreshTokenDO::getRefreshToken, refreshToken));
    }

    default OAuth2RefreshTokenDO selectByRefreshToken(String refreshToken){
        return selectOne(new LambdaQueryWrapper<OAuth2RefreshTokenDO>().eq(OAuth2RefreshTokenDO::getRefreshToken, refreshToken));
    }
}
