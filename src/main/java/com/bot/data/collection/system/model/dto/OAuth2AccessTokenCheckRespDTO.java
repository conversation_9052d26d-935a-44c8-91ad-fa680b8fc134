package com.bot.data.collection.system.model.dto;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * OAuth2.0 访问令牌的校验 Response DTO
 *
 * <AUTHOR>
 */
@Data
public class OAuth2AccessTokenCheckRespDTO implements Serializable {

    /**
     * 用户编号
     */
    private String userSid;
    /**
     * 用户组编号
     */
    private String userGroupSid;
    /**
     * 授权范围的数组
     */
    //private List<String> scopes;


}
