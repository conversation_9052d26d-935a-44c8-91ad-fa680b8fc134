package com.bot.data.collection.system.model.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bot.data.collection.common.pojo.BaseDO;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户和用户组关联
 *
 * <AUTHOR>
 */
@TableName("system_user_user_group")
@Builder
@Data
@EqualsAndHashCode(callSuper = true)
public class UserUserGroupDO extends BaseDO {

    /**
     * 自增主键
     */
    @TableId
    private String sid;
    /**
     * 用户 ID
     */
    private String userGroupSid;
    /**
     * 用户 ID
     */
    private String userSid;

}
