package com.bot.data.collection.system.mapper;

import com.bot.data.collection.common.pojo.PageResult;
import com.bot.data.collection.system.model.entity.UserGroupDO;
import com.bot.data.collection.common.mybatis.mapper.BaseMapperX;
import com.bot.data.collection.common.mybatis.query.LambdaQueryWrapperX;
import com.bot.data.collection.system.model.req.UserGroupPageReq;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


@Mapper
public interface UserGroupMapper extends BaseMapperX<UserGroupDO> {
    default Long selectCountById(String sid) {
        return selectCount(new LambdaQueryWrapperX<UserGroupDO>()
                .eqIfPresent(UserGroupDO::getSid, sid));
    }

    default Long selectCountByName(String sid, String name) {
        return selectCount(new LambdaQueryWrapperX<UserGroupDO>()
                .eqIfPresent(UserGroupDO::getName, name)
                .neIfPresent(UserGroupDO::getSid, sid));
    }

    default PageResult<UserGroupDO> selectPage(UserGroupPageReq reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<UserGroupDO>()
                .likeIfPresent(UserGroupDO::getName, reqVO.getName())
                .betweenIfPresent(UserGroupDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(UserGroupDO::getUpdateTime));
    }

    List<String> selectUserGroupSidListByRoleSid(@Param("roleSid") String roleSid);


}
