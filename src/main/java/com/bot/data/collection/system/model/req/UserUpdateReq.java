package com.bot.data.collection.system.model.req;

import com.bot.data.collection.system.model.base.UserBaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

@ApiModel(" 用户更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class UserUpdateReq extends UserBaseVO {


    @ApiModelProperty(value = "123", name = "用户sid", required = true)
    @NotEmpty(message = "用户sid不能为空")
    private String sid;
}
