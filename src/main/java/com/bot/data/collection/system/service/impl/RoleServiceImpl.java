package com.bot.data.collection.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.bot.data.collection.common.pojo.PageResult;
import com.bot.data.collection.common.utils.UUIDUtils;
import com.bot.data.collection.system.convert.RoleConvert;
import com.bot.data.collection.system.mapper.UserRoleMapper;
import com.bot.data.collection.system.model.entity.RoleDO;
import com.bot.data.collection.common.constants.CommonStatusEnum;
import com.bot.data.collection.system.constants.SystemErrorEnum;
import com.bot.data.collection.system.constants.RoleTypeEnum;
import com.bot.data.collection.common.exception.ServiceException;
import com.bot.data.collection.system.mapper.RoleMapper;
import com.bot.data.collection.system.mapper.RoleMenuMapper;
import com.bot.data.collection.system.mapper.UserGroupRoleMapper;
import com.bot.data.collection.system.model.entity.RoleMenuDO;
import com.bot.data.collection.system.model.entity.UserGroupRoleDO;
import com.bot.data.collection.system.model.entity.UserRoleDO;
import com.bot.data.collection.system.model.req.RoleCreateReq;
import com.bot.data.collection.system.model.req.RolePageReq;
import com.bot.data.collection.system.model.req.RoleUpdateReq;
import com.bot.data.collection.system.model.rsp.RoleInfoResp;
import com.bot.data.collection.system.model.rsp.RoleResp;
import com.bot.data.collection.system.service.MenuService;
import com.bot.data.collection.system.service.RoleService;
import com.bot.data.collection.system.service.UserGroupService;
import com.bot.data.collection.system.service.UserService;
import com.bot.data.collection.system.utils.SecurityUtils;
import com.google.common.annotations.VisibleForTesting;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class RoleServiceImpl implements RoleService {
    @Resource
    RoleMapper roleMapper;
    @Resource
    UserRoleMapper userRoleMapper;
    @Resource
    UserGroupRoleMapper userGroupRoleMapper;
    @Resource
    RoleMenuMapper roleMenuMapper;
    @Resource
    UserService userService;
    @Resource
    UserGroupService userGroupService;
    @Resource
    MenuService menuService;

    @Transactional
    @Override
    public String createRole(RoleCreateReq reqVO) {
        // 校验角色
        validateRoleNameUnique(null, reqVO.getName());
        //validateRoleCodeUnique(null, reqVO.getCode());
        // 插入到数据库
        RoleDO role = RoleConvert.INSTANCE.convert(reqVO);
        role.setStatus(CommonStatusEnum.EFFECTIVE.getCode());
        role.setType(RoleTypeEnum.CUSTOM.getType());
        role.setSid(UUIDUtils.getUUID());
        roleMapper.insert(role);

        //创建角色-用户关联信息
        if (CollUtil.isNotEmpty(reqVO.getUserSidList())) {
            List<UserRoleDO> userRoleList = new ArrayList<>();
            reqVO.getUserSidList().stream().forEach(userSid -> userRoleList.add(UserRoleDO.builder().sid(UUIDUtils.getUUID()).roleSid(role.getSid()).userSid(userSid).build()));
            userRoleMapper.insertBatch(userRoleList);
        }
        //创建角色-用户组关联信息
        if (CollUtil.isNotEmpty(reqVO.getUserGroupSidList())) {
            List<UserGroupRoleDO> userGroupRoleList = new ArrayList<>();
            reqVO.getUserGroupSidList().stream().forEach(userGroupSid -> userGroupRoleList.add(UserGroupRoleDO.builder().sid(UUIDUtils.getUUID()).roleSid(role.getSid()).userGroupSid(userGroupSid).build()));
            userGroupRoleMapper.insertBatch(userGroupRoleList);
        }
        //创建角色-菜单关联信息
        if (CollUtil.isNotEmpty(reqVO.getMenuSidList())) {
            List<RoleMenuDO> roleMenuList = new ArrayList<>();
            reqVO.getMenuSidList().stream().forEach(menuSid -> roleMenuList.add(RoleMenuDO.builder().sid(UUIDUtils.getUUID()).roleSid(role.getSid()).menuSid(menuSid).build()));
            roleMenuMapper.insertBatch(roleMenuList);
        }
        return role.getSid();
    }

    @Transactional
    @Override
    public void updateRole(RoleUpdateReq reqVO) {
        String loginUserSid = SecurityUtils.getLoginUserId();
        String sid = reqVO.getSid();
        // 校验是否可以更新
        validateRoleForUpdate(sid);
        // 校验角色的唯一字段是否重复
        validateRoleNameUnique(sid, reqVO.getName());
        //validateRoleCodeUnique(reqVO.getId(), reqVO.getCode());

        // 更新到数据库
        RoleDO updateObj = RoleConvert.INSTANCE.convert(reqVO);
        roleMapper.updateById(updateObj);

        //更新角色-用户关联信息
        updateRoleUserInfo(reqVO.getUserSidList(), userService.getRoleUserSidList(sid), sid, loginUserSid);
        //更新角色-用户组关联信息
        updateRoleUserGroupInfo(reqVO.getUserGroupSidList(), userGroupService.getRoleUserGroupSidList(sid), sid, loginUserSid);
        //更新角色-菜单关联信息
        updateRoleMenuInfo(reqVO.getMenuSidList(), menuService.getRoleMenuSidList(sid), sid, loginUserSid);
    }

    /**
     * 更新角色-用户关联信息
     * @param newSids 新用户sid集合
     * @param oldSids 老用户sid集合
     * @param roleSid 角色sid
     * @param loginUserSid 当前登录人sid
     */
    private void updateRoleUserInfo(List<String> newSids, List<String> oldSids, String roleSid, String loginUserSid) {
        List<String> addSids = null;
        List<String> removeSids = null;
        if (CollUtil.isEmpty(oldSids) && CollUtil.isNotEmpty(newSids)) {
            //全部新增
            addSids = newSids;
        } else if (CollUtil.isEmpty(newSids) && CollUtil.isNotEmpty(oldSids)) {
            //全部删除
            userRoleMapper.deleteUserByRoleSid(roleSid);
        }else if (CollUtil.isNotEmpty(newSids) && CollUtil.isNotEmpty(oldSids)) {
            addSids = newSids.stream()
                    .filter(e -> !oldSids.contains(e))
                    .collect(Collectors.toList());
            removeSids = oldSids.stream()
                    .filter(e -> !newSids.contains(e))
                    .collect(Collectors.toList());
        }
        if (CollUtil.isNotEmpty(addSids)) {
            List<UserRoleDO> list = new ArrayList<>();
            addSids.forEach(e -> list.add(UserRoleDO.builder().sid(UUIDUtils.getUUID()).roleSid(roleSid).userSid(e).build()));
            userRoleMapper.insertBatch(list);
        }
        if (CollUtil.isNotEmpty(removeSids)) {
            userRoleMapper.deleteBatchByUserSids(removeSids, roleSid, loginUserSid);
        }
    }

    private void updateRoleUserGroupInfo(List<String> newSids, List<String> oldSids, String roleSid, String loginUserSid) {
        List<String> addSids = null;
        List<String> removeSids = null;
        if (CollUtil.isEmpty(oldSids) && CollUtil.isNotEmpty(newSids)) {
            //全部新增
            addSids = newSids;
        } else if (CollUtil.isEmpty(newSids) && CollUtil.isNotEmpty(oldSids)) {
            //全部删除
            userGroupRoleMapper.deleteListByRoleId(roleSid);
        }else if (CollUtil.isNotEmpty(newSids) && CollUtil.isNotEmpty(oldSids)) {
            addSids = newSids.stream()
                    .filter(e -> !oldSids.contains(e))
                    .collect(Collectors.toList());
            removeSids = oldSids.stream()
                    .filter(e -> !newSids.contains(e))
                    .collect(Collectors.toList());
        }
        if (CollUtil.isNotEmpty(addSids)) {
            List<UserGroupRoleDO> list = new ArrayList<>();
            addSids.forEach(e -> list.add(UserGroupRoleDO.builder().sid(UUIDUtils.getUUID()).roleSid(roleSid).userGroupSid(e).build()));
            userGroupRoleMapper.insertBatch(list);
        }
        if (CollUtil.isNotEmpty(removeSids)) {
            userGroupRoleMapper.deleteBatchByUserGroupSids(removeSids, roleSid, loginUserSid);
        }
    }

    private void updateRoleMenuInfo(List<String> newSids, List<String> oldSids, String roleSid, String loginUserSid) {
        //newSids和oldSids一定不为空
        List<String> addSids = newSids.stream()
                .filter(e -> !oldSids.contains(e))
                .collect(Collectors.toList());
        List<String> removeSids = oldSids.stream()
                .filter(e -> !newSids.contains(e))
                .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(addSids)) {
            List<RoleMenuDO> list = new ArrayList<>();
            addSids.forEach(e -> list.add(RoleMenuDO.builder().sid(UUIDUtils.getUUID()).roleSid(roleSid).menuSid(e).build()));
            roleMenuMapper.insertBatch(list);
        }
        if (CollUtil.isNotEmpty(removeSids)) {
            roleMenuMapper.deleteBatchByMenuSids(removeSids, roleSid, loginUserSid);
        }
    }


    @Override
    public void deleteRole(String sid) {
        // 校验是否可以更新
        validateRoleForUpdate(sid);
        //该角色已与用户组/用户进行关联，将无法删除
        if (userRoleMapper.selectCountByRoleSid(sid) > 0) {
            throw new ServiceException(SystemErrorEnum.ROLE_RELATION_USER);
        }
        //该角色已与用户组/用户进行关联，将无法删除
        if (userGroupRoleMapper.selectCountByRoleSid(sid) > 0) {
            throw new ServiceException(SystemErrorEnum.ROLE_RELATION_USER_GROUP);
        }
        // 标记删除
        roleMapper.deleteById(sid);

        //删除用户组-角色关系
        //userGroupRoleMapper.deleteListByRoleId(id);
        //删除角色-权限关系
        //roleMenuMapper.deleteListByRoleId(id);
    }

    @Override
    public PageResult<RoleResp> getRolePage(RolePageReq reqVO) {
        PageResult<RoleDO> pageResult = roleMapper.selectPage(reqVO);
        PageResult<RoleResp> page = RoleConvert.INSTANCE.convertPage(pageResult);
        return page;
    }

    @Override
    public RoleInfoResp getRoleInfo(String sid) {
        RoleInfoResp roleInfoResp = null;
        if (ObjectUtil.isEmpty(sid)) {
            roleInfoResp = new RoleInfoResp();
        }else{
            RoleDO roleDO = roleMapper.selectById(sid);
            if (roleDO == null) {
                throw new ServiceException(SystemErrorEnum.ROLE_NOT_EXISTS);
            }
            roleInfoResp = RoleConvert.INSTANCE.convert(roleDO);
            roleInfoResp.setUserSidList(userService.getRoleUserSidList(sid));
            roleInfoResp.setUserGroupSidList(userGroupService.getRoleUserGroupSidList(sid));
            roleInfoResp.setMenuSidList(menuService.getRoleMenuSidList(sid));
        }
        roleInfoResp.setUserList(userService.getUserInfoList());
        roleInfoResp.setUserGroupList(userGroupService.getUserGroupInfoList());
        roleInfoResp.setMenuList(menuService.getMenuInfoTree());
        return roleInfoResp;
    }



    /**
     * 校验角色名称唯一
     * @param name
     */
    void validateRoleNameUnique(String sid, String name) {
        if (roleMapper.selectCountByName(sid, name) > 0) {
            throw new ServiceException(SystemErrorEnum.ROLE_NAME_EXISTS);
        }
    }
    /**
     * 校验角色编码唯一
     * @param sid
     * @param code
     */
    void validateRoleCodeUnique(String sid, String code) {
        if (roleMapper.selectCountByCode(sid, code) > 0) {
            throw new ServiceException(SystemErrorEnum.ROLE_CODE_EXISTS);
        }
    }

    /**
     * 校验角色是否可以被更新
     *
     * @param sid 角色编号
     */
    @VisibleForTesting
    void validateRoleForUpdate(String sid) {
        RoleDO roleDO = roleMapper.selectById(sid);
        if (roleDO == null) {
            throw new ServiceException(SystemErrorEnum.ROLE_NOT_EXISTS);
        }
        // 内置角色，不允许操作
        if (RoleTypeEnum.SYSTEM.getType().equals(roleDO.getType())) {
            throw new ServiceException(SystemErrorEnum.ROLE_CAN_NOT_UPDATE_SYSTEM_TYPE_ROLE);
        }
    }

}
