package com.bot.data.collection.system.model.base;

import com.bot.data.collection.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * 菜单 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class MenuBaseVO {

    @ApiModelProperty(value = "菜单名", name = "菜单名称", required = true)
    @NotBlank(message = "菜单名称不能为空")
    @Size(max = 50, message = "菜单名称长度不能超过50个字符")
    private String name;

    @ApiModelProperty(value = "sys:menu:add", name = "权限标识,仅菜单类型为按钮时，才需要传递")
    @Size(max = 100)
    private String permission;

    @ApiModelProperty(value = "枚举：（1：目录，2：菜单，3：按钮）", name = "类型", required = true)
    @NotNull(message = "菜单类型不能为空")
    private Integer type;

    @ApiModelProperty(value = "1", name = "显示顺序", required = true)
    private Integer sort;

    @ApiModelProperty(value = "1", name = "父菜单SID")
    @NotBlank(message = "父菜单SID不能为空")
    private String parentSid;

    @ApiModelProperty(value = "post", name = "菜单路径")
    @Size(max = 200, message = "路由地址不能超过200个字符")
    @NotBlank(message = "菜单路径不能为空")
    private String path;

    @ApiModelProperty(value = "/menu/list", name = "菜单图标,仅菜单类型为菜单或者目录时，才需要传")
    private String icon;

    @ApiModelProperty(value = "system/post/index", name = "组件路径,仅菜单类型为菜单时，才需要传")
    @Size(max = 200, message = "组件路径不能超过255个字符")
    private String component;

    @ApiModelProperty(value = "SystemUser", name = "组件名")
    private String componentName;

    @ApiModelProperty(value = "0：有效，1：无效", name = "状态", required = true)
    //@NotNull(message = "状态不能为空")
    private Integer status;

    @ApiModelProperty(value = "false", name = "是否可见", required = true)
    private Boolean visible;

    @ApiModelProperty(value = "false", name = "是否缓存", required = true)
    private Boolean keepAlive;

    @ApiModelProperty(value = "false", name = "是否总是显示", required = true)
    private Boolean alwaysShow;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "时间戳格式", name = "创建时间", required = true)
    @JsonFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;
    /**
     * 最后更新时间
     */
    @JsonFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @ApiModelProperty(value = "时间戳格式", name = "更新时间", required = true)
    private LocalDateTime updateTime;

}
