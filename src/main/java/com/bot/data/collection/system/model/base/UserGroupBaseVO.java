package com.bot.data.collection.system.model.base;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;
import java.util.List;

/**
 * 用户 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class UserGroupBaseVO {

    @ApiModelProperty(value = "zhangsan", name = "用户组名称")
    @NotEmpty(message = "用户组名称不能为空")
    private String name;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty(value = "zhangsan", name = "用户组下用户sid集合")
    private List<String> userSids;

}
