package com.bot.data.collection.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.bot.data.collection.common.pojo.PageResult;
import com.bot.data.collection.common.utils.UUIDUtils;
import com.bot.data.collection.server.mapper.ProjectInfoAuditorMapper;
import com.bot.data.collection.system.convert.UserGroupConvert;
import com.bot.data.collection.system.mapper.UserMapper;
import com.bot.data.collection.system.mapper.UserUserGroupMapper;
import com.bot.data.collection.system.model.dto.UserGroupInfoDTO;
import com.bot.data.collection.system.model.entity.UserGroupDO;
import com.bot.data.collection.system.constants.SystemErrorEnum;
import com.bot.data.collection.common.exception.ServiceException;
import com.bot.data.collection.system.mapper.UserGroupMapper;
import com.bot.data.collection.system.mapper.UserGroupRoleMapper;
import com.bot.data.collection.system.model.entity.UserUserGroupDO;
import com.bot.data.collection.system.model.rsp.UserGroupResp;
import com.bot.data.collection.system.service.UserGroupService;
import com.bot.data.collection.system.model.req.UserGroupCreateReq;
import com.bot.data.collection.system.model.req.UserGroupUpdateReq;
import com.bot.data.collection.system.service.UserService;
import com.bot.data.collection.system.utils.SecurityUtils;
import com.bot.data.collection.system.model.req.UserGroupPageReq;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.internal.StringUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class UserGroupServiceImpl implements UserGroupService {

    @Resource
    UserGroupMapper userGroupMapper;
    @Resource
    UserUserGroupMapper userUserGroupMapper;

    @Resource
    UserGroupRoleMapper userGroupRoleMapper;
    @Resource
    ProjectInfoAuditorMapper projectInfoAuditorMapper;
    @Resource
    UserService userService;

    @Resource
    UserMapper userMapper;

    @Transactional
    @Override
    public String createUserGroup(UserGroupCreateReq reqVO) {
        // 校验正确性
        validateUserGroupNameUnique(null, reqVO.getName());
        UserGroupDO userGroup = UserGroupDO.builder().sid(UUIDUtils.getUUID()).name(reqVO.getName()).remark(reqVO.getRemark()).build();

        // 新增用户组
        userGroupMapper.insert(userGroup);

        //绑定用户组用户关系
        List<String> userSids = reqVO.getUserSids();
        if (CollUtil.isNotEmpty(userSids)) {
            List<UserUserGroupDO> userGroupDOS = new ArrayList<>();
            for (String sid : userSids) {
                userGroupDOS.add(UserUserGroupDO.builder().sid(UUIDUtils.getUUID()).userGroupSid(userGroup.getSid()).userSid(sid).build());
            }
            userUserGroupMapper.insertBatch(userGroupDOS);
        }
        return userGroup.getSid();
    }

    @Transactional
    @Override
    public void updateUserGroup(UserGroupUpdateReq reqVO) {
        String sid = reqVO.getSid();
        // 校验正确性
        validateUserGroupExists(sid);
        validateUserGroupNameUnique(sid, reqVO.getName());
        //更新用户组
        UserGroupDO userGroup = UserGroupDO.builder().sid(sid).name(reqVO.getName()).remark(reqVO.getRemark()).build();
        userGroupMapper.updateById(userGroup);

        //:更新绑定的用户
        List<String> newUserSids = reqVO.getUserSids();
        List<String> oldUserSids = userMapper.selectUserSidListByGroupSid(sid);

        List<String> addUserSids = null;
        List<String> removeUserSids = null;
        if (CollUtil.isEmpty(oldUserSids) && CollUtil.isNotEmpty(newUserSids)) {
            //全部新增
            addUserSids = newUserSids;
        } else if (CollUtil.isEmpty(newUserSids) && CollUtil.isNotEmpty(oldUserSids)) {
            //全部删除
            userUserGroupMapper.deleteUserByGroupSid(sid);
        }else if (CollUtil.isNotEmpty(newUserSids) && CollUtil.isNotEmpty(oldUserSids)) {
            addUserSids = newUserSids.stream()
                    .filter(e -> !oldUserSids.contains(e))
                    .collect(Collectors.toList());
            removeUserSids = oldUserSids.stream()
                    .filter(e -> !newUserSids.contains(e))
                    .collect(Collectors.toList());
        }
        if (CollUtil.isNotEmpty(addUserSids)) {
            List<UserUserGroupDO> userGroupDOS = new ArrayList<>();
            addUserSids.forEach(e -> userGroupDOS.add(UserUserGroupDO.builder().sid(UUIDUtils.getUUID()).userGroupSid(sid).userSid(e).build()));
            userUserGroupMapper.insertBatch(userGroupDOS);
        }
        if (CollUtil.isNotEmpty(removeUserSids)) {
            userUserGroupMapper.deleteBatchByUserSids(removeUserSids, sid, SecurityUtils.getLoginUserId());
        }
    }

    @Transactional
    @Override
    public void deleteUserGroup(String sid) {
        // 校验正确性
        validateUserGroupExists(sid);
        // 删除用户组
        userGroupMapper.deleteById(sid);
        // 删除用户关联’用户‘数据
        userUserGroupMapper.deleteUserByGroupSid(sid);
        // 删除用户关联’角色‘数据
        userGroupRoleMapper.deleteByUserGroupSid(sid);
        //用户组将与所有相关的用户/项目解除关联
        projectInfoAuditorMapper.deleteInfoByUserGroupSid(sid);
    }

    @Override
    public UserGroupResp getUserGroup(String sid) {
        UserGroupDO userGroup = userGroupMapper.selectById(sid);
        if (userGroup == null) {
            throw new ServiceException(SystemErrorEnum.USER_GROUP_NOT_EXISTS);
        }
        UserGroupResp resp = UserGroupConvert.INSTANCE.convert(userGroup);
        //放入已有的用户
        //resp.setUserList(userService.getUserInfoList());
        resp.setUserSidList(userService.getUserGroupUserSidList(sid));
        return resp;
    }

    @Override
    public PageResult<UserGroupResp> getUserGroupPage(UserGroupPageReq reqVO) {
        PageResult<UserGroupDO> pageResult = userGroupMapper.selectPage(reqVO);
        PageResult<UserGroupResp> page = UserGroupConvert.INSTANCE.convertPage(pageResult);
        return page;
    }

    @Override
    public List<UserGroupInfoDTO> getUserGroupInfoList() {
        return UserGroupConvert.INSTANCE.convertList(userGroupMapper.selectList());
    }

    @Override
    public List<String> getRoleUserGroupSidList(String roleSid) {
        return userGroupMapper.selectUserGroupSidListByRoleSid(roleSid);
    }



    /**
     * 校验用户组存在
     * @param sid
     */
    //@VisibleForTesting
    void validateUserGroupExists(String sid) {
        if (StringUtil.isBlank(sid)) {
            return;
        }
        if (userGroupMapper.selectCountById(sid) == 0) {
            throw new ServiceException(SystemErrorEnum.USER_GROUP_NOT_EXISTS);
        }
    }

    /**
     * 校验用户组名唯一
     * @param name
     */
    void validateUserGroupNameUnique(String sid, String name) {
        if (userGroupMapper.selectCountByName(sid, name) > 0) {
            throw new ServiceException(SystemErrorEnum.USER_GROUP_NAME_EXISTS);
        }
    }
}
