package com.bot.data.collection.system.model.req;

import com.bot.data.collection.system.model.base.UserBaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


@ApiModel("用户创建 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class UserCreateReq extends UserBaseVO {

    @ApiModelProperty(value = "xxx", name = "密码")
    //@NotEmpty(message = "密码不能为空")
    private String password;
}
