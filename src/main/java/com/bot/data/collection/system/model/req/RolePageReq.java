package com.bot.data.collection.system.model.req;

import com.bot.data.collection.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@ApiModel("角色分页 Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class RolePageReq extends PageParam {

    @ApiModelProperty(value = "管理员", name = "角色名称", required = true)
    private String name;
}
