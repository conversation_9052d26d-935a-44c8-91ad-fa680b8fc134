package com.bot.data.collection.system.service;

import com.bot.data.collection.system.model.dto.MenuInfoDTO;
import com.bot.data.collection.system.model.req.MenuCreateReq;
import com.bot.data.collection.system.model.req.MenuUpdateReq;
import com.bot.data.collection.system.model.rsp.MenuResq;

import java.util.List;

public interface MenuService {
    String createMenu(MenuCreateReq reqVO);

    void updateMenu(MenuUpdateReq reqVO);

    void deleteMenu(String sid);

    MenuResq getMenu(String sid);

    List<MenuResq> getMenuTree();

    /**
     * 获得用户有权限的菜单
     * @return
     */
    List<MenuResq> getUserMenuTree();

    List<MenuInfoDTO> getMenuInfoTree();

    /**
     * 查询角色所绑定的菜单sid
     * @param roleSid
     * @return
     */
    List<String> getRoleMenuSidList(String roleSid);

}
