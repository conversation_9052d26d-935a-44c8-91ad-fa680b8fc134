package com.bot.data.collection.system.model.req;

import com.bot.data.collection.system.model.base.RoleBaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@ApiModel("角色创建 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class RoleCreateReq extends RoleBaseVO {

    @ApiModelProperty(value = "110", name = "角色关联用户集合")
    private List<String> userSidList;

    @ApiModelProperty(value = "110", name = "角色关联用户组集合")
    private List<String> userGroupSidList;

    @ApiModelProperty(value = "110", name = "角色关联菜单集合")
    private List<String> menuSidList;
}
