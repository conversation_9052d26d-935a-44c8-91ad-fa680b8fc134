package com.bot.data.collection.system.convert;

import com.bot.data.collection.system.model.entity.OAuth2AccessTokenDO;
import com.bot.data.collection.system.model.rsp.AuthLoginResp;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface AuthConvert {
    AuthConvert INSTANCE = Mappers.getMapper(AuthConvert.class);
    AuthLoginResp convert(OAuth2AccessTokenDO bean);
}
