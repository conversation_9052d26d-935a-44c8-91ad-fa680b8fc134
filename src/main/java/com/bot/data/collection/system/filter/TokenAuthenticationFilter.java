package com.bot.data.collection.system.filter;

import cn.hutool.core.util.StrUtil;
import com.bot.data.collection.system.constants.AuthConstants;
import com.bot.data.collection.system.convert.OAuth2TokenConvert;
import com.bot.data.collection.system.mapper.UserMapper;
import com.bot.data.collection.system.model.entity.OAuth2AccessTokenDO;
import com.bot.data.collection.system.model.entity.UserDO;
import com.bot.data.collection.system.service.OAuth2TokenService;
import com.bot.data.collection.system.service.UserService;
import com.bot.data.collection.system.utils.LoginUser;
import com.bot.data.collection.system.utils.SecurityFrameworkUtils;
import com.bot.data.collection.system.model.dto.OAuth2AccessTokenCheckRespDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.annotation.Resource;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Configuration
@RequiredArgsConstructor
public class TokenAuthenticationFilter extends OncePerRequestFilter {

    @Resource
    OAuth2TokenService oauth2TokenService;
    @Resource
    UserMapper userMapper;
    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain) throws ServletException, IOException {
        // 打印请求的URL
        //System.out.println("过滤器 Request URL: " + request.getRequestURL());

        //获取请求头的 token
        String token = SecurityFrameworkUtils.obtainAuthorization(request, AuthConstants.tokenHeader);

        if (StrUtil.isNotEmpty(token)) {
            // 1.1 基于 token 构建登录用户
            OAuth2AccessTokenDO oAuth2AccessTokenDO = oauth2TokenService.checkAccessToken(token);
            if (oAuth2AccessTokenDO != null) {
                OAuth2AccessTokenCheckRespDTO accessToken = OAuth2TokenConvert.INSTANCE.convert(oAuth2AccessTokenDO);
                LoginUser loginUser = null;
                if (accessToken != null) {
                    loginUser = new LoginUser();
                    loginUser.setSid(accessToken.getUserSid());
                    UserDO user = userMapper.selectById(accessToken.getUserSid());
                    loginUser.setName(user.getUsername());
                    //loginUser.setUserGroupId(accessToken.getUserGroupId());
                    loginUser.setAccessToken(loginUser.getAccessToken());
                }

                // 2. （本地测试时）设置登陆用户

                // 2. 设置当前用户
                if (loginUser != null) {
                    SecurityFrameworkUtils.setLoginUser(loginUser, request);
                }
            }

        }

        // 继续过滤链
        chain.doFilter(request, response);
    }


}
