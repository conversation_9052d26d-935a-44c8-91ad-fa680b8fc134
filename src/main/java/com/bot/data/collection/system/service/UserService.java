package com.bot.data.collection.system.service;

import com.bot.data.collection.system.model.dto.UserInfoDTO;
import com.bot.data.collection.system.model.entity.UserDO;
import com.bot.data.collection.common.pojo.PageResult;
import com.bot.data.collection.system.model.req.UserCreateReq;
import com.bot.data.collection.system.model.req.UserPageReq;
import com.bot.data.collection.system.model.req.UserPasswordUpdateReq;
import com.bot.data.collection.system.model.req.UserUpdateReq;
import com.bot.data.collection.system.model.rsp.UserResp;

import java.util.List;

public interface UserService {
    /**
     * 通过用户名查询用户
     *
     * @param username 用户名
     * @return 用户对象信息
     */
    UserDO getUserByUsername(String username);

    UserDO getUserByMobile(String mobile);
    /**
     * 判断密码是否匹配
     *
     * @param rawPassword 未加密的密码
     * @param encodedPassword 加密后的密码
     * @return 是否匹配
     */
    boolean isPasswordMatch(String rawPassword, String encodedPassword);

    String createUser(UserCreateReq reqVO);
    /**
     * 修改用户
     *
     * @param reqVO 用户信息
     */
    void updateUser(UserUpdateReq reqVO);

    /**
     * 修改用户密码
     * @param reqVO
     */
    void updateUserPassword(UserPasswordUpdateReq reqVO);
    /**
     * 删除用户
     *
     * @param sid 用户编号
     */
    void deleteUser(String sid);
    /**
     * 获得用户分页列表
     *
     * @param reqVO 分页条件
     * @return 分页列表
     */
    PageResult<UserResp> getUserPage(UserPageReq reqVO);

    UserResp getUser(String sid);

    List<UserInfoDTO> getUserInfoList();

    List<String> getUserGroupUserSidList(String userGroupSid);

    List<UserInfoDTO> getGroupUserList(String groupSid);

    /**
     * 根据角色查询sid集合
     * @param roleSid
     * @return
     */
    List<String> getRoleUserSidList(String roleSid);


}
