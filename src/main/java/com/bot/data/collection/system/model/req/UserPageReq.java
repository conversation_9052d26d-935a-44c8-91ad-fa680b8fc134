package com.bot.data.collection.system.model.req;

import com.bot.data.collection.common.utils.DateUtils;
import com.bot.data.collection.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

@ApiModel("用户分页 Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class UserPageReq extends PageParam {

    @ApiModelProperty("用户账号（模糊匹配）")
    private String username;

    @ApiModelProperty("手机号码（模糊匹配）")
    private String mobile;

    @ApiModelProperty("手机号码（模糊匹配）")
    private String email;

    @ApiModelProperty(value = "0：有效，1：无效", name = "状态")
    private Integer status;

    @ApiModelProperty(value = "[2022-07-01 00:00:00, 2022-07-01 23:59:59]", name = "创建时间")
    @DateTimeFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
