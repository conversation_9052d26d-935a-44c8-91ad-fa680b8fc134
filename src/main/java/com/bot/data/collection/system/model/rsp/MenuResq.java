package com.bot.data.collection.system.model.rsp;

import com.bot.data.collection.system.model.base.MenuBaseVO;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

@ApiModel(value = "权限信息 Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MenuResq extends MenuBaseVO {
    @NotNull(message = "菜单 ID 不能为空")
    private String sid;

    private List<MenuResq> children;
}
