package com.bot.data.collection.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.bot.data.collection.common.utils.UUIDUtils;
import com.bot.data.collection.system.convert.MenuConvert;
import com.bot.data.collection.system.model.dto.MenuInfoDTO;
import com.bot.data.collection.system.model.entity.MenuDO;
import com.bot.data.collection.system.constants.SystemErrorEnum;
import com.bot.data.collection.system.constants.MenuTypeEnum;
import com.bot.data.collection.common.exception.ServiceException;
import com.bot.data.collection.system.mapper.MenuMapper;
import com.bot.data.collection.system.model.req.MenuCreateReq;
import com.bot.data.collection.system.model.req.MenuUpdateReq;
import com.bot.data.collection.system.model.rsp.MenuResq;
import com.bot.data.collection.system.service.MenuService;
import com.bot.data.collection.system.utils.SecurityFrameworkUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
public class MenuServiceImpl implements MenuService {
    @Resource
    MenuMapper menuMapper;
    @Override
    public String createMenu(MenuCreateReq reqVO) {
        // 校验父菜单存在
        validateParentMenu(reqVO.getParentSid(), null);
        // 校验菜单（自己）
        validateMenu(reqVO.getParentSid(), reqVO.getName(), null);

        // 插入数据库
        MenuDO menu = MenuConvert.INSTANCE.convert(reqVO);
        initMenuProperty(menu);
        menu.setSid(UUIDUtils.getUUID());
        menuMapper.insert(menu);
        return menu.getSid();
    }

    @Override
    public void updateMenu(MenuUpdateReq reqVO) {
        // 校验更新的菜单是否存在
        if (menuMapper.selectById(reqVO.getSid()) == null) {
            throw new ServiceException(SystemErrorEnum.MENU_NOT_EXISTS);
        }
        // 校验父菜单存在
        validateParentMenu(reqVO.getParentSid(), reqVO.getSid());
        // 校验菜单（自己）
        validateMenu(reqVO.getParentSid(), reqVO.getName(), reqVO.getSid());

        // 更新到数据库
        MenuDO updateObject = MenuConvert.INSTANCE.convert(reqVO);
        initMenuProperty(updateObject);
        menuMapper.updateById(updateObject);
    }

    @Override
    public void deleteMenu(String sid) {
        if (menuMapper.selectById(sid) == null) {
            throw new ServiceException(SystemErrorEnum.MENU_NOT_EXISTS);
        }
        // 校验是否还有子菜单
        if (menuMapper.selectCountByParentId(sid) > 0) {
            throw new ServiceException(SystemErrorEnum.MENU_EXISTS_CHILDREN);
        }
        // 标记删除
        menuMapper.deleteById(sid);
        // 删除授予给角色的权限（角色-权限中间表数据）子菜单也将一并删除
        //roleMenuMapper.deleteListByMenuId(id);
    }

    @Override
    public MenuResq getMenu(String sid) {
        return MenuConvert.INSTANCE.convert(menuMapper.selectById(sid));
    }

    @Override
    public List<MenuResq> getMenuTree() {
        return menuMapper.selectMenuTree();
    }

    @Override
    public List<MenuResq> getUserMenuTree() {
        String loginUserSid = SecurityFrameworkUtils.getLoginUserId();
        if (StringUtils.isBlank(loginUserSid)) {
            return null;
        }
        List<MenuResq> menuResqs = menuMapper.selectMenuTree();

        List<String> menuSids = menuMapper.selectMenuSidList(loginUserSid);
        return filterMenu(menuResqs, menuSids);
    }

    @Override
    public List<MenuInfoDTO> getMenuInfoTree() {
        return menuMapper.selectMenuInfoTree();
    }

    @Override
    public List<String> getRoleMenuSidList(String roleSid) {
        return menuMapper.selectMenuSidListByRoleSid(roleSid);
    }

    /**
     * ，
     * @param menuResqs 层
     * @param menuSids
     * @return
     */
    private List<MenuResq> filterMenu(List<MenuResq> menuResqs, List<String> menuSids) {
        List<MenuResq> menuResqList = null;
        if (CollUtil.isNotEmpty(menuResqs)) {
            if (CollUtil.isNotEmpty(menuSids)) {
                menuResqList = new ArrayList<>();
                //menuResqList = menuResqs.stream().filter(item -> menuSids.contains(item.getSid())).collect(Collectors.toList());
                for (MenuResq menuResq : menuResqs) {
                    //如果菜单已关联

                    //先判断子集是否空
                    if (CollUtil.isNotEmpty(menuResq.getChildren())) {
                        //f/ilterMenu(menuResq.getChildren(), menuSids);
                        //menuResqList.add(menuResq);
                        //判断子集是否有被选中的
                        List<MenuResq> children = filterMenu(menuResq.getChildren(), menuSids);
                        if (CollUtil.isNotEmpty(children)) {
                            //添加上父元素自己
                            //menuResq.setChildren();
                            menuResq.setChildren(children);
                            menuResqList.add(menuResq);
                        }
                    }else {
                        //最小子集，判断是否存在
                        if (menuSids.contains(menuResq.getSid())) {
                            menuResqList.add(menuResq);
                        }
                    }

                }
            }else {
                menuResqList = menuResqs;
            }
        }
        return menuResqList;
    }


    /**
     * 校验父菜单是否合法
     * <p>
     * 1. 不能设置自己为父菜单
     * 2. 父菜单不存在
     * 3. 父菜单必须是 {@link MenuTypeEnum#MENU} 菜单类型
     *
     * @param parentId 父菜单编号
     * @param sid  当前菜单编号
     */
    void validateParentMenu(String parentId, String sid) {
        if (StringUtils.isBlank(parentId)){
            return;
        }
        // 不能设置自己为父菜单
        if (parentId.equals(sid)) {
            throw new ServiceException(SystemErrorEnum.MENU_PARENT_ERROR);
        }

        if (!"0".equals(parentId)){
            MenuDO menu = menuMapper.selectById(parentId);
            // 父菜单不存在
            if (menu == null) {
                throw new ServiceException(SystemErrorEnum.MENU_PARENT_NOT_EXISTS);
            }
            // 父菜单必须是目录或者菜单类型
            if (!MenuTypeEnum.DIR.getType().equals(menu.getType())
                    && !MenuTypeEnum.MENU.getType().equals(menu.getType())) {
                throw new ServiceException(SystemErrorEnum.MENU_PARENT_NOT_DIR_OR_MENU);
            }
        }

    }

    /**
     * 校验菜单是否合法
     * <p>
     * 1. 校验相同父菜单编号下，是否存在相同的菜单名
     *
     * @param name     菜单名字
     * @param parentSid 父菜单编号
     * @param sid       菜单编号
     */
    void validateMenu(String parentSid, String name, String sid) {
        MenuDO menu = menuMapper.selectByParentIdAndName(parentSid, name);
        if (menu == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的菜单
        if (StringUtils.isBlank(sid) || !menu.getSid().equals(sid)){
            throw new ServiceException(SystemErrorEnum.MENU_NAME_DUPLICATE);
        }
    }
    /**
     * 初始化菜单的通用属性。
     * <p>
     * 例如说，只有目录或者菜单类型的菜单，才设置 icon
     *
     * @param menu 菜单
     */
    private void initMenuProperty(MenuDO menu) {
        // 菜单为按钮类型时，无需 component、icon、path 属性，进行置空
        if (MenuTypeEnum.BUTTON.getType().equals(menu.getType())) {
            menu.setComponent("");
            menu.setComponentName("");
            menu.setIcon("");
            menu.setPath("");
        }
    }
}
