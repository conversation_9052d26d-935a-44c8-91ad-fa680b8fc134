package com.bot.data.collection.system.model.rsp;

import com.bot.data.collection.common.utils.DateUtils;
import com.bot.data.collection.system.model.base.RoleBaseVO;
import com.bot.data.collection.system.model.dto.MenuInfoDTO;
import com.bot.data.collection.system.model.dto.UserGroupInfoDTO;
import com.bot.data.collection.system.model.dto.UserInfoDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@ApiModel(value = "角色信息 Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class RoleInfoResp extends RoleBaseVO {

    @ApiModelProperty(value = "110", name = "角色sid", required = true)
    private String sid;

    @ApiModelProperty(value = "110", name = "用户组集合")
    private List<UserGroupInfoDTO> userGroupList;

    @ApiModelProperty(value = "110", name = "角色关联用户组sid集合")
    private List<String> userGroupSidList;

    @ApiModelProperty(value = "110", name = "用户集合")
    private List<UserInfoDTO> userList;

    @ApiModelProperty(value = "110", name = "角色关联用户集合")
    private List<String> userSidList;

    @ApiModelProperty(value = "110", name = "菜单集合")
    private List<MenuInfoDTO> menuList;

    @ApiModelProperty(value = "110", name = "角色关联菜单集合")
    private List<String> menuSidList;

    @ApiModelProperty(value = "0：有效 1:无效", name = "状态", required = true)
    private Integer status;

    @ApiModelProperty(value = "zs", name = "创建人", required = true)
    private String creator;

    @ApiModelProperty(value = "2022-07-01 00:00:00", name = "创建时间", required = true)
    @JsonFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "2022-07-01 00:00:00", name = "更新时间", required = true)
    @JsonFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime updateTime;
}
