package com.bot.data.collection.system.service.impl;

import com.bot.data.collection.system.utils.SecurityFrameworkUtils;
import com.bot.data.collection.system.service.PermissionService;
import com.bot.data.collection.system.service.SecurityFrameworkService;
import lombok.AllArgsConstructor;

import javax.annotation.Resource;


/**
 * 默认的 {@link SecurityFrameworkService} 实现类
 *
 * <AUTHOR>
 */
@AllArgsConstructor
public class SecurityFrameworkServiceImpl implements SecurityFrameworkService {

    @Resource
    PermissionService permissionService;

    @Override
    public boolean hasPermission(String permission) {
        return hasAnyPermissions(permission);
    }

    @Override
    public boolean hasAnyPermissions(String... permissions) {
        return permissionService.hasAnyPermissions(SecurityFrameworkUtils.getLoginUserId(), permissions);
    }

    @Override
    public boolean hasRole(String role) {
        return hasAnyRoles(role);
    }

    @Override
    public boolean hasAnyRoles(String... roles) {
        return permissionService.hasAnyRoles(SecurityFrameworkUtils.getLoginUserId(), roles);
    }

    /*@Override
    public boolean hasScope(String scope) {
        return hasAnyScopes(scope);
    }

    @Override
    public boolean hasAnyScopes(String... scope) {
        LoginUser user = SecurityFrameworkUtils.getLoginUser();
        if (user == null) {
            return false;
        }
        return CollUtil.containsAny(user.getScopes(), Arrays.asList(scope));
    }*/

}
