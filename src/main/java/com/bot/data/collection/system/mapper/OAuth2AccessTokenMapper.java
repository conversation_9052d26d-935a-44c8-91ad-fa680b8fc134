package com.bot.data.collection.system.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bot.data.collection.system.model.entity.OAuth2AccessTokenDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface OAuth2AccessTokenMapper extends BaseMapper<OAuth2AccessTokenDO> {
    default OAuth2AccessTokenDO selectByAccessToken(String accessToken) {
        return selectOne(new LambdaQueryWrapper<OAuth2AccessTokenDO>().eq(OAuth2AccessTokenDO::getAccessToken, accessToken));
    }

    default List<OAuth2AccessTokenDO> selectListByRefreshToken(String refreshToken){
        return selectList(new LambdaQueryWrapper<OAuth2AccessTokenDO>().eq(OAuth2AccessTokenDO::getRefreshToken, refreshToken));
    }
}
