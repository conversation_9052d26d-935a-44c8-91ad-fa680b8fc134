package com.bot.data.collection.system.controller;

import com.bot.data.collection.common.pojo.CommonResult;
import com.bot.data.collection.system.constants.SystemErrorEnum;
import com.bot.data.collection.common.exception.ServiceException;
import com.bot.data.collection.system.model.req.MenuCreateReq;
import com.bot.data.collection.system.model.req.MenuUpdateReq;
import com.bot.data.collection.system.model.rsp.MenuResq;
import com.bot.data.collection.system.service.MenuService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@Api(tags = "菜单管理")
@RestController
@RequestMapping("/menu")
@Validated
public class MenuController {

    @Resource
    private MenuService menuService;

    @PostMapping("/create")
    @ApiOperation(value = "新增菜单")
    //@PermitAll
    //@PreAuthorize("@ss.hasPermission('system:user:create')")
    public CommonResult<String> createMenu(@Valid @RequestBody MenuCreateReq reqVO) {
        return CommonResult.success(menuService.createMenu(reqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改菜单")
    //@PermitAll
    //@PreAuthorize("@ss.hasPermission('system:user:update')")
    public CommonResult<Boolean> updateMenu(@Valid @RequestBody MenuUpdateReq reqVO) {
        menuService.updateMenu(reqVO);
        return CommonResult.success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除菜单")
    //@PermitAll
    //@PreAuthorize("@ss.hasPermission('system:user:delete')")
    public CommonResult<Boolean> deleteMenu(@ApiParam(name = "sid", value = "123", required = true) @RequestParam("sid") String sid) {
        if (StringUtils.isBlank(sid)) {
            throw new ServiceException(SystemErrorEnum.PARAM_IS_NULL);
        }
        menuService.deleteMenu(sid);
        return CommonResult.success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "查询菜单详情")
    //@PermitAll
    //@PreAuthorize("@ss.hasPermission('system:user:delete')")
    public CommonResult<MenuResq> getMenu(@ApiParam(name = "sid", value = "123", required = true) @RequestParam("sid") String sid) {
        if (StringUtils.isBlank(sid)) {
            throw new ServiceException(SystemErrorEnum.PARAM_IS_NULL);
        }
        ;
        return CommonResult.success(menuService.getMenu(sid));
    }


    @GetMapping("/tree")
    @ApiOperation(value = "查询菜单树")
    //@PermitAll
    public CommonResult<List<MenuResq>> getMenuTree() {
        List<MenuResq> menuResqs = menuService.getMenuTree();
        return CommonResult.success(menuResqs);
    }

    @GetMapping("/userMenu")
    @ApiOperation(value = "查询用户菜单树")
    //@PermitAll
    public CommonResult<List<MenuResq>> getUserMenuTree() {
        List<MenuResq> menuResqs = menuService.getUserMenuTree();
        return CommonResult.success(menuResqs);
    }


}
