package com.bot.data.collection.system.model.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bot.data.collection.common.pojo.BaseDO;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户和角色关联
 *
 * <AUTHOR>
 */
@TableName("system_user_group_role")
@Builder
@Data
@EqualsAndHashCode(callSuper = true)
public class UserGroupRoleDO extends BaseDO {

    /**
     * 自增主键
     */
    @TableId
    private String sid;
    /**
     * 用户组 ID
     */
    private String userGroupSid;
    /**
     * 角色 ID
     */
    private String roleSid;

}
