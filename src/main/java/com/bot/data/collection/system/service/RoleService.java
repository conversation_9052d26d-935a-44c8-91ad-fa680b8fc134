package com.bot.data.collection.system.service;

import com.bot.data.collection.common.pojo.PageResult;
import com.bot.data.collection.system.model.req.RoleCreateReq;
import com.bot.data.collection.system.model.req.RolePageReq;
import com.bot.data.collection.system.model.req.RoleUpdateReq;
import com.bot.data.collection.system.model.rsp.RoleInfoResp;
import com.bot.data.collection.system.model.rsp.RoleResp;

public interface RoleService {
    String createRole(RoleCreateReq reqVO);

    void updateRole(RoleUpdateReq reqVO);

    void deleteRole(String sid);

    PageResult<RoleResp> getRolePage(RolePageReq reqVO);

    RoleInfoResp getRoleInfo(String sid);

}
