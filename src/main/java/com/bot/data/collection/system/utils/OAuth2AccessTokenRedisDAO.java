package com.bot.data.collection.system.utils;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.bot.data.collection.common.utils.CollectionUtils;
import com.bot.data.collection.common.utils.JsonUtils;
import com.bot.data.collection.system.model.entity.OAuth2AccessTokenDO;
import com.bot.data.collection.system.constants.RedisKeyConstants;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.TimeUnit;


/**
 * {@link OAuth2AccessTokenDO} 的 RedisDAO
 *
 * <AUTHOR>
 */
@Repository
public class OAuth2AccessTokenRedisDAO {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 获取 OAuth2 访问令牌对象
     * @param accessToken
     * @return
     */
    public OAuth2AccessTokenDO get(String accessToken) {
        String redisKey = formatKey(accessToken);
        return JsonUtils.parseObject(stringRedisTemplate.opsForValue().get(redisKey), OAuth2AccessTokenDO.class);
    }

    /**
     * 设置 OAuth2 访问令牌对象
     * @param accessTokenDO
     */
    public void set(OAuth2AccessTokenDO accessTokenDO) {
        String redisKey = formatKey(accessTokenDO.getAccessToken());
        // 清理多余字段
        accessTokenDO.setUpdater(null);
        accessTokenDO.setUpdateTime(null);
        accessTokenDO.setCreateTime(null);
        accessTokenDO.setCreator(null);
        accessTokenDO.setDeleted(null);
        //计算过期时间
        long time = LocalDateTimeUtil.between(LocalDateTime.now(), accessTokenDO.getExpiresTime(), ChronoUnit.SECONDS);
        if (time > 0) {
            stringRedisTemplate.opsForValue().set(redisKey, JsonUtils.toJsonString(accessTokenDO), time, TimeUnit.SECONDS);
        }
    }

    /**
     * 删除 OAuth2 访问令牌对象
     * @param accessToken
     */
    public void delete(String accessToken) {
        String redisKey = formatKey(accessToken);
        stringRedisTemplate.delete(redisKey);
    }

    public void deleteList(Collection<String> accessTokens) {
        List<String> redisKeys = CollectionUtils.convertList(accessTokens, OAuth2AccessTokenRedisDAO::formatKey);
        stringRedisTemplate.delete(redisKeys);
    }

    /**
     * 拼接 Token
     * @param accessToken
     * @return
     */
    private static String formatKey(String accessToken) {
        return String.format(RedisKeyConstants.OAUTH2_ACCESS_TOKEN, accessToken);
    }

}
