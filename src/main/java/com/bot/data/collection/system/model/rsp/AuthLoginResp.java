package com.bot.data.collection.system.model.rsp;

import com.bot.data.collection.common.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Schema(description = "登录 Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AuthLoginResp {

    @ApiModelProperty(value = "江南七怪", name = "用户sid", required = true)
    private String userSid;

    @ApiModelProperty(value = "xxx", name = "访问令牌", required = true)
    private String accessToken;

    @ApiModelProperty(value = "xxx", name = "刷新令牌", required = true)
    private String refreshToken;

    @ApiModelProperty(value = "2022-07-01 00:00:00", name = "过期时间", required = true)
    @JsonFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND )
    private LocalDateTime expiresTime;

}
