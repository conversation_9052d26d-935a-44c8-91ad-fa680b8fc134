package com.bot.data.collection.system.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.toolkit.Db;
import com.bot.data.collection.system.model.entity.UserUserGroupDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

@Mapper
public interface UserUserGroupMapper extends BaseMapper<UserUserGroupDO> {
    /**
     * 批量插入，适合大量数据插入
     *
     * @param entities 实体们
     */
    default void insertBatch(Collection<UserUserGroupDO> entities) {
        Db.saveBatch(entities);
    }

    void deleteBatchByUserSids(@Param("userSids") List<String> userSids, @Param("groupSid") String groupSid, @Param("updater") String updater);
    default void deleteUserByGroupSid(String userGroupSid) {
        delete(new LambdaQueryWrapper<UserUserGroupDO>().eq(UserUserGroupDO::getUserGroupSid, userGroupSid));
    }

    default void deleteByUserSid(String userSid) {
        delete(Wrappers.lambdaUpdate(UserUserGroupDO.class).eq(UserUserGroupDO::getUserSid, userSid));
    }


}
