package com.bot.data.collection.system.controller;

import com.bot.data.collection.common.pojo.CommonResult;
import com.bot.data.collection.common.pojo.PageResult;
import com.bot.data.collection.system.constants.SystemErrorEnum;
import com.bot.data.collection.common.exception.ServiceException;
import com.bot.data.collection.system.model.req.RoleCreateReq;
import com.bot.data.collection.system.model.req.RolePageReq;
import com.bot.data.collection.system.model.req.RoleUpdateReq;
import com.bot.data.collection.system.model.rsp.RoleInfoResp;
import com.bot.data.collection.system.model.rsp.RoleResp;
import com.bot.data.collection.system.service.RoleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang.StringUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;

@Api(tags = "角色管理")
@RestController
@RequestMapping("/role")
@Validated
public class RoleController {

    @Resource
    RoleService roleService;

    @PostMapping("/create")
    @ApiOperation(value = "新增角色")
    //@PreAuthorize("@ss.hasPermission('system:role:create')")
    public CommonResult<String> createRole(@Valid @RequestBody RoleCreateReq reqVO) {
        return CommonResult.success(roleService.createRole(reqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改角色")
    //@PreAuthorize("@ss.hasPermission('system:role:update')")
    public CommonResult<Boolean> updateRole(@Valid @RequestBody RoleUpdateReq reqVO) {
        roleService.updateRole(reqVO);
        return CommonResult.success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除角色")
    //@PreAuthorize("@ss.hasPermission('system:role:delete')")
    public CommonResult<Boolean> deleteRole(@ApiParam(name = "sid", value = "1024", required = true) @RequestParam("sid") String sid) {
        if (StringUtils.isBlank(sid)) {
            throw new ServiceException(SystemErrorEnum.PARAM_IS_NULL);
        }
        roleService.deleteRole(sid);
        return CommonResult.success(true);
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得角色分页")
    //@PermitAll
    //@PreAuthorize("@ss.hasPermission('system:user:list')")
    public CommonResult<PageResult<RoleResp>> getRolePage(@Valid @RequestBody RolePageReq reqVO) {
        // 获得用户分页列表
        PageResult<RoleResp> pageResult = roleService.getRolePage(reqVO);
        return CommonResult.success(pageResult);
    }

    @GetMapping("/get")
    @ApiOperation(value = "查询角色详情")
    @PermitAll
    //@PreAuthorize("@ss.hasPermission('system:role:delete')")
    public CommonResult<RoleInfoResp> getRoleInfo(@ApiParam(name = "sid", value = "1024") @RequestParam(value = "sid", required = false ) String sid) {
        return CommonResult.success(roleService.getRoleInfo(sid));
    }

}
