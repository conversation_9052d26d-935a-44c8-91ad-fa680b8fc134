package com.bot.data.collection.system.model.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bot.data.collection.common.pojo.BaseDO;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 角色和菜单关联
 *
 * <AUTHOR>
 */
@TableName("system_role_menu")
@Builder
@Data
@EqualsAndHashCode(callSuper = true)
public class RoleMenuDO extends BaseDO {


    @TableId
    private String sid;
    /**
     * 角色ID
     */
    private String roleSid;
    /**
     * 菜单ID
     */
    private String menuSid;

}
