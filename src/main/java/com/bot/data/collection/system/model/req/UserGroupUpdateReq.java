package com.bot.data.collection.system.model.req;

import com.bot.data.collection.system.model.base.UserGroupBaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
@ApiModel("用户组更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class UserGroupUpdateReq extends UserGroupBaseVO {

    @ApiModelProperty(value = "1024", name = "用户组id", required = true)
    @NotEmpty(message = "用户组sid不能为空")
    private String sid;
}
