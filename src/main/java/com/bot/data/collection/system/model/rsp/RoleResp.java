package com.bot.data.collection.system.model.rsp;

import com.bot.data.collection.common.utils.DateUtils;
import com.bot.data.collection.system.model.base.RoleBaseVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@ApiModel(value = "角色信息 Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class RoleResp extends RoleBaseVO {

    @ApiModelProperty(value = "110", name = "角色sid", required = true)
    private String sid;

    @ApiModelProperty(value = "0：有效 1:无效", name = "状态", required = true)
    private Integer status;

    @ApiModelProperty(value = "zs", name = "创建人", required = true)
    private String creator;

    @ApiModelProperty(value = "2022-07-01 00:00:00", name = "创建时间", required = true)
    @JsonFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "2022-07-01 00:00:00", name = "更新时间", required = true)
    @JsonFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime updateTime;
}
