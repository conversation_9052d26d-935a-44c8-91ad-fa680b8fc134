package com.bot.data.collection.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;

import com.bot.data.collection.common.utils.UUIDUtils;
import com.bot.data.collection.system.model.entity.OAuth2RefreshTokenDO;
import com.bot.data.collection.system.constants.AuthConstants;
import com.bot.data.collection.system.constants.SystemErrorEnum;
import com.bot.data.collection.common.exception.ServiceException;
import com.bot.data.collection.system.model.entity.OAuth2AccessTokenDO;
import com.bot.data.collection.common.utils.CollectionUtils;
import com.bot.data.collection.common.utils.DateUtils;
import com.bot.data.collection.system.utils.OAuth2AccessTokenRedisDAO;
import com.bot.data.collection.system.mapper.OAuth2AccessTokenMapper;
import com.bot.data.collection.system.mapper.OAuth2RefreshTokenMapper;
import com.bot.data.collection.system.service.OAuth2TokenService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;


/**
 * OAuth2.0 Token Service 实现类
 *
 * <AUTHOR>
 */
@Service
public class OAuth2TokenServiceImpl implements OAuth2TokenService {

    @Resource
    private OAuth2AccessTokenMapper oauth2AccessTokenMapper;
    @Resource
    private OAuth2RefreshTokenMapper oauth2RefreshTokenMapper;
    @Resource
    private OAuth2AccessTokenRedisDAO oauth2AccessTokenRedisDAO;

    @Override
    @Transactional
    public OAuth2AccessTokenDO createAccessToken(String userSid) {
        // 创建刷新令牌
        OAuth2RefreshTokenDO refreshTokenDO = createOAuth2RefreshToken(userSid);
        // 创建访问令牌
        return createOAuth2AccessToken(refreshTokenDO);
    }

    @Override
    public OAuth2AccessTokenDO refreshAccessToken(String refreshToken) {
        // 查询访问令牌
        OAuth2RefreshTokenDO refreshTokenDO = oauth2RefreshTokenMapper.selectByRefreshToken(refreshToken);
        if (refreshTokenDO == null) {
            throw new ServiceException(SystemErrorEnum.BAD_REQUEST.getCode(), "无效的刷新令牌");
        }

        // 移除相关的访问令牌
        List<OAuth2AccessTokenDO> accessTokenDOs = oauth2AccessTokenMapper.selectListByRefreshToken(refreshToken);
        if (CollUtil.isNotEmpty(accessTokenDOs)) {
            oauth2AccessTokenMapper.deleteBatchIds(CollectionUtils.convertSet(accessTokenDOs, OAuth2AccessTokenDO::getSid));
            oauth2AccessTokenRedisDAO.deleteList(CollectionUtils.convertSet(accessTokenDOs, OAuth2AccessTokenDO::getAccessToken));
        }

        // 已过期的情况下，删除刷新令牌
        if (DateUtils.isExpired(refreshTokenDO.getExpiresTime())) {
            oauth2RefreshTokenMapper.deleteById(refreshTokenDO.getSid());
            throw new ServiceException(SystemErrorEnum.UNAUTHORIZED.getCode(), "刷新令牌已过期");
        }

        // 创建访问令牌
        return createOAuth2AccessToken(refreshTokenDO);
    }

    @Override
    public OAuth2AccessTokenDO getAccessToken(String accessToken) {
        //1、 优先从 Redis 中获取
        OAuth2AccessTokenDO accessTokenDO = oauth2AccessTokenRedisDAO.get(accessToken);
        if (accessTokenDO != null) {
            return accessTokenDO;
        }

        //2、Redis获取不到时，从 MySQL 中获取
        accessTokenDO = oauth2AccessTokenMapper.selectByAccessToken(accessToken);

        //3、 如果在 MySQL 存在，则写入 Redis
        if (accessTokenDO != null && !DateUtils.isExpired(accessTokenDO.getExpiresTime())) {
            oauth2AccessTokenRedisDAO.set(accessTokenDO);
        }
        return accessTokenDO;
    }

    @Override
    public OAuth2AccessTokenDO checkAccessToken(String accessToken) {
        OAuth2AccessTokenDO accessTokenDO = getAccessToken(accessToken);
        if (accessTokenDO == null) {
            return null;
            //throw new ServiceException(SystemErrorEnum.UNAUTHORIZED.getCode(), "访问令牌不存在");
        }
        if (DateUtils.isExpired(accessTokenDO.getExpiresTime())) {
            return null;
            //throw new ServiceException(SystemErrorEnum.UNAUTHORIZED.getCode(), "访问令牌已过期");
        }
        /*if (accessTokenDO == null) {
            throw new ServiceException(SystemErrorEnum.UNAUTHORIZED.getCode(), "访问令牌不存在");
        }
        if (DateUtils.isExpired(accessTokenDO.getExpiresTime())) {
            throw new ServiceException(SystemErrorEnum.UNAUTHORIZED.getCode(), "访问令牌已过期");
        }*/
        return accessTokenDO;
    }

    @Override
    public OAuth2AccessTokenDO removeAccessToken(String accessToken) {
        // 删除访问令牌
        OAuth2AccessTokenDO accessTokenDO = oauth2AccessTokenMapper.selectByAccessToken(accessToken);
        if (accessTokenDO != null) {
            oauth2AccessTokenMapper.deleteById(accessTokenDO.getSid());
            oauth2AccessTokenRedisDAO.delete(accessToken);
            // 删除刷新令牌
            oauth2RefreshTokenMapper.deleteByRefreshToken(accessTokenDO.getRefreshToken());
        }
        return accessTokenDO;
    }



    private OAuth2RefreshTokenDO createOAuth2RefreshToken(String userSid) {
        OAuth2RefreshTokenDO refreshToken = new OAuth2RefreshTokenDO()
                .setSid(UUIDUtils.getUUID())
                .setRefreshToken(IdUtil.fastSimpleUUID())
                .setUserSid(userSid)
                .setExpiresTime(LocalDateTime.now().plusSeconds(AuthConstants.REFRESH_TOKEN_VALIDITY_SECONDS));
        oauth2RefreshTokenMapper.insert(refreshToken);
        return refreshToken;
    }

    private OAuth2AccessTokenDO createOAuth2AccessToken(OAuth2RefreshTokenDO refreshTokenDO) {
        OAuth2AccessTokenDO accessTokenDO = new OAuth2AccessTokenDO().setAccessToken(IdUtil.fastSimpleUUID())
                .setSid(UUIDUtils.getUUID())
                .setUserSid(refreshTokenDO.getUserSid())
                .setRefreshToken(refreshTokenDO.getRefreshToken())
                .setExpiresTime(LocalDateTime.now().plusSeconds(AuthConstants.ACCESS_TOKEN_VALIDITY_SECONDS));
        //accessTokenDO.setTenantId(TenantContextHolder.getTenantId()); // 手动设置租户编号，避免缓存到 Redis 的时候，无对应的租户编号
        oauth2AccessTokenMapper.insert(accessTokenDO);
        // 记录到 Redis 中
        oauth2AccessTokenRedisDAO.set(accessTokenDO);
        return accessTokenDO;
    }

}
