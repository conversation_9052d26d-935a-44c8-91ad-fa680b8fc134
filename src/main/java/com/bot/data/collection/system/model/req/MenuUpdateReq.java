package com.bot.data.collection.system.model.req;

import com.bot.data.collection.system.model.base.MenuBaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;


@ApiModel("菜单更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class MenuUpdateReq extends MenuBaseVO {

    @ApiModelProperty(value = "123", name = "菜单编号", required = true)
    @NotEmpty(message = "菜单sid不能为空")
    private String sid;
}
