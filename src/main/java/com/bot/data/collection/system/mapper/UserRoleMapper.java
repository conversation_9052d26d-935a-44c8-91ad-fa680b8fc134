package com.bot.data.collection.system.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.toolkit.Db;
import com.bot.data.collection.common.mybatis.mapper.BaseMapperX;
import com.bot.data.collection.system.model.entity.UserRoleDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;


@Mapper
public interface UserRoleMapper extends BaseMapperX<UserRoleDO> {

    default void insertBatch(Collection<UserRoleDO> entities) {
        Db.saveBatch(entities);
    }

    int deleteBatchByUserSids(@Param("userSids") List<String> userSids, @Param("roleSid") String roleSid, @Param("updater") String updater);
    default void deleteByUserSid(String userSid) {
        delete(Wrappers.lambdaUpdate(UserRoleDO.class).eq(UserRoleDO::getUserSid, userSid));
    }

    default void deleteUserByRoleSid(String roleSid) {
        delete(new LambdaQueryWrapper<UserRoleDO>().eq(UserRoleDO::getRoleSid, roleSid));
    }

    default Long selectCountByRoleSid(String roleSid) {
        return selectCount(UserRoleDO::getRoleSid, roleSid);
    }

}
