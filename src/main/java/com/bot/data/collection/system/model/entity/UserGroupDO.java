package com.bot.data.collection.system.model.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bot.data.collection.common.pojo.BaseDO;
import lombok.*;

/**
 * 用户组 DO
 *
 * <AUTHOR>
 */
@TableName(value = "system_user_group") // 由于 SQL Server 的 system_user 是关键字，所以使用 system_users , autoResultMap = true
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserGroupDO extends BaseDO {

    /**
     * 用户ID
     */
    @TableId
    private String sid;
    /**
     * 组名
     */
    private String name;
    /**
     * 备注
     */
    private String remark;
    /**
     * 父级组 ID
     */
    //private Long parentId;

    private Integer status;
}
