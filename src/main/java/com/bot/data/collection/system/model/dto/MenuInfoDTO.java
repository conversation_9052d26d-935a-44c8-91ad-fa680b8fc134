package com.bot.data.collection.system.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 用户 DO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MenuInfoDTO {

    /**
     * 用户ID
     */
    private String sid;
    /**
     * 用户账号
     */
    private String name;

    private List<MenuInfoDTO> children;
}
