package com.bot.data.collection.system.convert;

import com.bot.data.collection.common.pojo.PageResult;
import com.bot.data.collection.system.model.entity.RoleDO;
import com.bot.data.collection.system.model.req.RoleCreateReq;
import com.bot.data.collection.system.model.req.RoleUpdateReq;
import com.bot.data.collection.system.model.rsp.RoleInfoResp;
import com.bot.data.collection.system.model.rsp.RoleResp;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface RoleConvert {
    RoleConvert INSTANCE = Mappers.getMapper(RoleConvert.class);

    RoleDO convert(RoleCreateReq reqVO);

    RoleDO convert(RoleUpdateReq reqVO);

    RoleInfoResp convert(RoleDO reqVO);

    PageResult<RoleResp> convertPage(PageResult<RoleDO> pageResult);
}
