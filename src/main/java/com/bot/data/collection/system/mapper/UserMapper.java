package com.bot.data.collection.system.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bot.data.collection.system.model.entity.UserDO;
import com.bot.data.collection.common.mybatis.mapper.BaseMapperX;
import com.bot.data.collection.common.mybatis.query.LambdaQueryWrapperX;
import com.bot.data.collection.common.pojo.PageResult;
import com.bot.data.collection.system.model.req.UserPageReq;
import org.apache.commons.lang.StringUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface UserMapper extends BaseMapperX<UserDO> {
    default UserDO selectByUsername(String username) {
        return selectOne(new LambdaQueryWrapper<UserDO>().eq(UserDO::getUsername, username));
    }
    default UserDO selectByMobile(String mobile) {
        return selectOne(new LambdaQueryWrapper<UserDO>().eq(UserDO::getMobile, mobile));
    }

    default Long selectCountBySid(String sid) {
        return selectCount(new LambdaQueryWrapper<UserDO>().eq(UserDO::getSid, sid));
    }


    default Long selectCountByMobile(String sid, String mobile) {
        LambdaQueryWrapper<UserDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserDO::getMobile, mobile);
        if (StringUtils.isNotBlank(sid)) {
            wrapper.ne(UserDO::getSid, sid);
        }
        return selectCount(wrapper);
    }



    default PageResult<UserDO> selectPage(UserPageReq reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<UserDO>()
                .likeIfPresent(UserDO::getUsername, reqVO.getUsername())
                .likeIfPresent(UserDO::getMobile, reqVO.getMobile())
                .likeIfPresent(UserDO::getEmail, reqVO.getEmail())
                .eqIfPresent(UserDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(UserDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(UserDO::getUpdateTime));
    }

    List<UserDO> selectGroupUserList(@Param("groupSid") String groupSid);

    List<String> selectUserSidListByGroupSid(@Param("groupSid") String groupSid);

    List<String> selectUserSidListByRoleSid(@Param("roleSid") String roleSid);

}
