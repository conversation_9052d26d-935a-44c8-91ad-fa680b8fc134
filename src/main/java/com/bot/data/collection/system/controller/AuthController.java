package com.bot.data.collection.system.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.bot.data.collection.common.utils.Des3Utils;
import com.bot.data.collection.common.utils.RedisUtils;
import com.bot.data.collection.system.constants.AuthConstants;
import com.bot.data.collection.common.pojo.CommonResult;
import com.bot.data.collection.system.constants.LoginLogTypeEnum;
import com.bot.data.collection.system.utils.SecurityFrameworkUtils;
import com.bot.data.collection.system.model.req.AuthLoginReq;
import com.bot.data.collection.system.model.rsp.AuthLoginResp;
import com.bot.data.collection.system.service.AuthService;
import com.wf.captcha.ArithmeticCaptcha;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Api(tags = "认证")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/auth")
public class AuthController {

    @Autowired
    AuthService authService;
    private final RedisUtils redisUtils;

    @PermitAll
    @ApiOperation(value = "使用手机号密码登录")
    @PostMapping(value = "/login")
    public CommonResult<AuthLoginResp> login(@RequestBody @Valid AuthLoginReq reqVO) {
        return CommonResult.success(authService.login(reqVO));
    }

    //@PermitAll
    @ApiOperation(value = "登出系统")
    @GetMapping(value = "/logout")
    public CommonResult<Boolean> logout(HttpServletRequest request) {
        String token = SecurityFrameworkUtils.obtainAuthorization(request, AuthConstants.tokenHeader);
        if (StrUtil.isNotBlank(token)) {
            authService.logout(token, LoginLogTypeEnum.LOGOUT_SELF.getType());
        }
        return CommonResult.success(true);
    }

    @PermitAll
    @ApiOperation(value = "刷新令牌")
    @PostMapping("/refresh-token")
    public CommonResult<AuthLoginResp> refreshToken(@ApiParam(name = "refreshToken", value = "xxx", required = true) @RequestParam("refreshToken") String refreshToken) {
        return CommonResult.success(authService.refreshToken(refreshToken));
    }

    @GetMapping(value = "/captcha")
    @ApiOperation(value = "验证码")
    @PermitAll
    public CommonResult captcha() {
        ArithmeticCaptcha captcha = new ArithmeticCaptcha(100, 40, 2);
        String key = IdWorker.get32UUID();
        String verKey = Des3Utils.encode(key);
        redisUtils.set(verKey, captcha.text(), 10, TimeUnit.MINUTES);
        return CommonResult.success(Map.of("verKey", key, "verCode", captcha.toBase64()));
    }
}
