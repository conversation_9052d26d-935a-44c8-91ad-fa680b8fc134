package com.bot.data.collection.system.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 菜单类型枚举类
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum SystemGroupEnum {

    SYSTEM_SUPER_ADMIN(Long.parseLong("1"), "system_super_admin"), // 系统超级管理员
    AIGC_SUPER_ADMIN(Long.parseLong("2"), "aigc_super_admin"), // aigc超级管理员
    AIGC_ADMIN(Long.parseLong("3"), "aigc_admin") // aigc管理员
    ;

    /**
     * 主键id
     */
    private final Long id;

    private final String name;

}
