package com.bot.data.collection.system.mapper;

import com.baomidou.mybatisplus.extension.toolkit.Db;
import com.bot.data.collection.common.mybatis.mapper.BaseMapperX;
import com.bot.data.collection.system.model.entity.RoleMenuDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

@Mapper
public interface RoleMenuMapper extends BaseMapperX<RoleMenuDO> {
    default void insertBatch(Collection<RoleMenuDO> entities) {
        Db.saveBatch(entities);
    }

    int deleteBatchByMenuSids(@Param("menuSids") List<String> userSids, @Param("roleSid") String roleSid, @Param("updater") String updater);


}
