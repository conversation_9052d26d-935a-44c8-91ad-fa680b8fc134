package com.bot.data.collection.system.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.toolkit.Db;
import com.bot.data.collection.system.model.entity.UserGroupRoleDO;
import com.bot.data.collection.common.mybatis.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

@Mapper
public interface UserGroupRoleMapper extends BaseMapperX<UserGroupRoleDO> {
    default void insertBatch(Collection<UserGroupRoleDO> entities) {
        Db.saveBatch(entities);
    }

    int deleteBatchByUserGroupSids(@Param("groupSids") List<String> groupSids, @Param("roleSid") String roleSid, @Param("updater") String updater);
    default void deleteByUserGroupSid(String userGroupSid) {
        delete(Wrappers.lambdaUpdate(UserGroupRoleDO.class).eq(UserGroupRoleDO::getUserGroupSid, userGroupSid));
    }


    default void deleteListByRoleId(String roleSid) {
        delete(new LambdaQueryWrapper<UserGroupRoleDO>().eq(UserGroupRoleDO::getRoleSid, roleSid));
    }

    default Long selectCountByRoleSid(String roleSid) {
        return selectCount(UserGroupRoleDO::getRoleSid, roleSid);
    }


}
