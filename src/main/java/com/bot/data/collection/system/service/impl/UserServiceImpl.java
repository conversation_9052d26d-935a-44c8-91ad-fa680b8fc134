package com.bot.data.collection.system.service.impl;

import com.bot.data.collection.common.utils.UUIDUtils;
import com.bot.data.collection.common.utils.ValidatorUtil;
import com.bot.data.collection.server.mapper.ProjectInfoAuditorMapper;
import com.bot.data.collection.system.constants.AuthConstants;
import com.bot.data.collection.system.convert.UserConvert;
import com.bot.data.collection.system.mapper.UserRoleMapper;
import com.bot.data.collection.system.model.dto.UserInfoDTO;
import com.bot.data.collection.system.model.entity.UserDO;
import com.bot.data.collection.common.constants.CommonStatusEnum;
import com.bot.data.collection.system.constants.SystemErrorEnum;
import com.bot.data.collection.common.exception.ServiceException;
import com.bot.data.collection.system.mapper.UserMapper;
import com.bot.data.collection.system.mapper.UserUserGroupMapper;
import com.bot.data.collection.common.pojo.PageResult;
import com.bot.data.collection.system.model.req.UserCreateReq;
import com.bot.data.collection.system.model.req.UserPageReq;
import com.bot.data.collection.system.model.req.UserPasswordUpdateReq;
import com.bot.data.collection.system.model.req.UserUpdateReq;
import com.bot.data.collection.system.model.rsp.UserResp;
import com.bot.data.collection.system.service.UserService;
import com.bot.data.collection.system.utils.SecurityFrameworkUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;


@Service
@Slf4j
public class UserServiceImpl implements UserService {
    @Resource
    private UserMapper userMapper;
    @Resource
    private UserUserGroupMapper userUserGroupMapper;
    @Resource
    private UserRoleMapper userRoleMapper;
    @Resource
    private ProjectInfoAuditorMapper projectInfoAuditorMapper;
    @Resource
    private PasswordEncoder passwordEncoder;

    @Override
    public UserDO getUserByUsername(String username) {
        return userMapper.selectByUsername(username);
    }

    @Override
    public UserDO getUserByMobile(String mobile) {
        return userMapper.selectByMobile(mobile);
    }

    @Override
    public boolean isPasswordMatch(String rawPassword, String encodedPassword) {
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }

    @Transactional
    @Override
    public String createUser(UserCreateReq reqVO) {
        // 校验正确性
        validateMobileUnique(null, reqVO.getMobile());
        //validateEmailUnique(null, reqVO.getEmail());

        // 新增用户
        UserDO user = UserConvert.INSTANCE.convert(reqVO);
        user.setStatus(CommonStatusEnum.EFFECTIVE.getCode()); // 默认开启
        user.setPassword(passwordEncoder.encode(AuthConstants.USER_DEFAULT_PASSWORD)); // 密码默认赋值，且加密密码
        user.setSid(UUIDUtils.getUUID());
        userMapper.insert(user);

        return user.getSid();
    }

    @Transactional
    @Override
    public void updateUser(UserUpdateReq reqVO) {
        String sid = reqVO.getSid();
        // 校验正确性
        validateUserExists(sid);
        validateMobileUnique(sid, reqVO.getMobile());
        //validateEmailUnique(id, reqVO.getEmail());

        // 更新用户
        UserDO updateObj = UserConvert.INSTANCE.convertUserDO(reqVO);
        userMapper.updateById(updateObj);
    }

    @Override
    public void updateUserPassword(UserPasswordUpdateReq reqVO) {
        if (!ValidatorUtil.validatePassword(reqVO.getNewPassword())) {
            throw new ServiceException(SystemErrorEnum.USER_PASS_FORMAT_ERROR);
        }
        UserDO userDO = userMapper.selectById(SecurityFrameworkUtils.getLoginUserId());
        if (!isPasswordMatch(reqVO.getOldPassword(), userDO.getPassword())) {
            throw new ServiceException(SystemErrorEnum.USER_OLD_PASS_ERROR);
        }
        userDO.setPassword(passwordEncoder.encode(reqVO.getNewPassword()));
        userMapper.updateById(userDO);
    }

    @Transactional
    @Override
    public void deleteUser(String sid) {
        // 校验正确性
        validateUserExists(sid);
        // 删除用户
        userMapper.deleteById(sid);
        //删除后，该用户将与所有相关的角色/用户组/项目解除关联，并且无法继续登录使用此平台
        // 删除用户关联用户组数据
        userUserGroupMapper.deleteByUserSid(sid);
        // 删除用户关联用户角色数据
        userRoleMapper.deleteByUserSid(sid);
        //删除项目关联
        projectInfoAuditorMapper.deleteInfoByUserSid(sid);

    }

    @Override
    public PageResult<UserResp> getUserPage(UserPageReq reqVO) {
        PageResult<UserDO> pageResult = userMapper.selectPage(reqVO);
        //待定
        PageResult<UserResp> page = UserConvert.INSTANCE.convertPage(pageResult);
        return page;
    }

    @Override
    public UserResp getUser(String sid) {
        validateUserExists(sid);
        return UserConvert.INSTANCE.convert(userMapper.selectById(sid));
    }

    @Override
    public List<UserInfoDTO> getUserInfoList() {
        return UserConvert.INSTANCE.convertList(userMapper.selectList());
    }

    @Override
    public List<String> getUserGroupUserSidList(String userGroupSid) {
        return userMapper.selectUserSidListByGroupSid(userGroupSid);
    }

    @Override
    public List<UserInfoDTO> getGroupUserList(String groupSid) {
        return UserConvert.INSTANCE.convertList(userMapper.selectGroupUserList(groupSid));
    }

    @Override
    public List<String> getRoleUserSidList(String roleSid) {
        return userMapper.selectUserSidListByRoleSid(roleSid);
    }


    /**
     * 校验用户存在
     * @param sid
     */
    void validateUserExists(String sid) {
        if (StringUtils.isBlank(sid)) {
            return;
        }
        if (userMapper.selectCountBySid(sid) == 0) {
            throw new ServiceException(SystemErrorEnum.USER_NOT_EXISTS);
        }
    }


    /**
     * 校验手机号唯一
     * @param sid
     * @param mobile
     */
    void validateMobileUnique(String sid, String mobile) {
        if (!ValidatorUtil.validatePhoneNumber(mobile)) {
            throw new ServiceException(SystemErrorEnum.USER_MOBILE_FORMAT_ERR);
        }
        if (userMapper.selectCountByMobile(sid, mobile) > 0) {
            throw new ServiceException(SystemErrorEnum.USER_MOBILE_EXISTS);
        }
    }

    /**
     * 校验邮箱唯一
     * @param id
     * @param email
     */
    /*void validateEmailUnique(Long id, String email) {
        if (userMapper.selectCountByEmail(id, email) > 0) {
            throw new ServiceException(SystemErrorEnum.USER_EMAIL_EXISTS);
        }
    }*/


}
