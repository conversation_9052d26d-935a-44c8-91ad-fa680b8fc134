package com.bot.data.collection.system.model.req;

import com.bot.data.collection.system.model.base.RoleBaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.List;


@ApiModel("角色更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class RoleUpdateReq extends RoleBaseVO {

    @ApiModelProperty(value = "123", name = "角色id", required = true)
    @NotEmpty(message = "角色sid不能为空")
    private String sid;

    @ApiModelProperty(value = "110", name = "角色关联用户集合")
    private List<String> userSidList;

    @ApiModelProperty(value = "110", name = "角色关联用户组集合")
    private List<String> userGroupSidList;

    @ApiModelProperty(value = "110", name = "角色关联菜单集合")
    private List<String> menuSidList;
}
