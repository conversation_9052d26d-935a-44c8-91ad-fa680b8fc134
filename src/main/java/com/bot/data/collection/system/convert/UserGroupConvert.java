package com.bot.data.collection.system.convert;

import com.bot.data.collection.common.pojo.PageResult;
import com.bot.data.collection.system.model.dto.UserGroupInfoDTO;
import com.bot.data.collection.system.model.entity.UserGroupDO;
import com.bot.data.collection.system.model.rsp.UserGroupResp;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface UserGroupConvert {
    UserGroupConvert INSTANCE = Mappers.getMapper(UserGroupConvert.class);

    UserGroupResp convert(UserGroupDO bean);

    List<UserGroupResp> convert(List<UserGroupDO> bean);

    List<UserGroupInfoDTO> convertList(List<UserGroupDO> bean);

    PageResult<UserGroupResp> convertPage(PageResult<UserGroupDO> pageResult);
}
