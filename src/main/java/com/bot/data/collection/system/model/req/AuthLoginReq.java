package com.bot.data.collection.system.model.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;

@ApiModel("账号密码登录 Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AuthLoginReq {

    @ApiModelProperty(value = "xxx", name = "登录账号", required = true)
    @NotEmpty(message = "登录账号不能为空")
    //@Length(min = 4, max = 16, message = "账号长度为 4-16 位")
    //@Pattern(regexp = "^[A-Za-z0-9]+$", message = "账号格式为数字以及字母")
    private String mobile;

    @ApiModelProperty(value = "xxx", name = "密码", required = true)
    @NotEmpty(message = "密码不能为空")
    private String password;

    @ApiModelProperty(value = "xxx", name = "验证码", required = true)
    @NotEmpty(message = "验证码不能为空")
    private String verCode;

    @ApiModelProperty(value = "xxx", name = "验证码key", required = true)
    @NotEmpty(message = "验证码key不能为空")
    private String verKey;

}