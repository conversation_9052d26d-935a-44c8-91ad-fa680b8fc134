package com.bot.data.collection.system.mapper;

import com.bot.data.collection.system.model.dto.MenuInfoDTO;
import com.bot.data.collection.system.model.entity.MenuDO;
import com.bot.data.collection.common.mybatis.mapper.BaseMapperX;
import com.bot.data.collection.system.model.rsp.MenuResq;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Set;

@Mapper
public interface MenuMapper extends BaseMapperX<MenuDO> {

    @Select("select permission from system_menu where sid in (select menu_sid from system_role_menu where role_sid IN(select sid from system_role where sid in (select role_sid from system_user_group_role where user_group_sid in (select sid from system_user_group where sid in (select user_group_sid from system_user_user_group where user_sid = #{userSid} and deleted = 0) and `status` = 0 and deleted = 0) and deleted = 0) and `status` = 0 and deleted = 0) and deleted = 0) and permission != '' and permission is not null and `status` = 0 and deleted = 0;")
    Set<String> selectMenuPermissionsByUserId(@Param("userSid") String userSid);

    default MenuDO selectByParentIdAndName(String parentSid, String name) {
        return selectOne(MenuDO::getParentSid, parentSid, MenuDO::getName, name);
    }

    default Long selectCountByParentId(String parentSid) {
        return selectCount(MenuDO::getParentSid, parentSid);
    }

    List<MenuResq> selectMenuTree();

    List<String> selectMenuSidList(@Param("userSid") String userSid);

    List<MenuInfoDTO> selectMenuInfoTree();

    List<String> selectMenuSidListByRoleSid(String roleSid);
}
