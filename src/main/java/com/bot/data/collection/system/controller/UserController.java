package com.bot.data.collection.system.controller;

import com.bot.data.collection.common.pojo.CommonResult;
import com.bot.data.collection.system.constants.SystemErrorEnum;
import com.bot.data.collection.common.exception.ServiceException;
import com.bot.data.collection.common.pojo.PageResult;
import com.bot.data.collection.system.model.dto.UserInfoDTO;
import com.bot.data.collection.system.model.req.UserCreateReq;
import com.bot.data.collection.system.model.req.UserPageReq;
import com.bot.data.collection.system.model.req.UserPasswordUpdateReq;
import com.bot.data.collection.system.model.req.UserUpdateReq;
import com.bot.data.collection.system.model.rsp.UserResp;
import com.bot.data.collection.system.service.UserService;
import com.bot.data.collection.system.utils.SecurityFrameworkUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;
import java.util.List;

@Api(tags = "用户管理")
@RestController
@RequestMapping("/user")
@Validated
public class UserController {
    @Resource
    UserService userService;

    @PostMapping("/create")
    @ApiOperation(value = "新增用户")
    //@PermitAll
    //@PreAuthorize("@ss.hasPermission('system:user:create')")
    public CommonResult<String> createUser(@Valid @RequestBody UserCreateReq reqVO) {
        String sid = userService.createUser(reqVO);
        return CommonResult.success(sid);
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改用户")
    //@PermitAll
    //@PreAuthorize("@ss.hasPermission('system:user:update')")
    public CommonResult<Boolean> updateUser(@Valid @RequestBody UserUpdateReq reqVO) {
        userService.updateUser(reqVO);
        return CommonResult.success(true);
    }

    @PostMapping("/updatePassword")
    @ApiOperation(value = "修改用户密码")
    //@PermitAll
    //@PreAuthorize("@ss.hasPermission('system:user:update')")
    public CommonResult<Boolean> updatePassword(@Valid @RequestBody UserPasswordUpdateReq reqVO) {
        userService.updateUserPassword(reqVO);
        return CommonResult.success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除用户")
    //@PermitAll
    //@PreAuthorize("@ss.hasPermission('system:user:delete')")
    public CommonResult<Boolean> deleteUser(@ApiParam(name = "sid", value = "123", required = true) @RequestParam("sid") String sid) {
        if (StringUtils.isBlank(sid)) {
            throw new ServiceException(SystemErrorEnum.PARAM_IS_NULL);
        }
        userService.deleteUser(sid);
        return CommonResult.success(true);
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得用户分页列表")
    //@PermitAll
    //@PreAuthorize("@ss.hasPermission('system:user:list')")
    public CommonResult<PageResult<UserResp>> getUserPage(@Valid @RequestBody UserPageReq reqVO) {
        // 获得用户分页列表
        PageResult<UserResp> pageResult = userService.getUserPage(reqVO);
        return CommonResult.success(pageResult);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得用户详情")
    //@PermitAll
    //@PreAuthorize("@ss.hasPermission('system:user:query')")
    public CommonResult<UserResp> getUser(@ApiParam(name = "sid", value = "123", required = true) @RequestParam("sid") String sid) {
        if (StringUtils.isBlank(sid)) {
            throw new ServiceException(SystemErrorEnum.PARAM_IS_NULL);
        }
        return CommonResult.success(userService.getUser(sid));
    }

    @GetMapping("/getLoginUser")
    @ApiOperation(value = "获得当前登录用户详情")
    //@PreAuthorize("@ss.hasPermission('system:user:query')")
    public CommonResult<UserResp> getLoginUser() {
        return CommonResult.success(userService.getUser(SecurityFrameworkUtils.getLoginUserId()));
    }


    @GetMapping("/list")
    @ApiOperation(value = "获得用户列表")
    //@PermitAll
    //@PreAuthorize("@ss.hasPermission('system:user:list')")
    public CommonResult<List<UserInfoDTO>> getUserList() {
        return CommonResult.success(userService.getUserInfoList());
    }

    @GetMapping("/groupUserList")
    @ApiOperation(value = "根据用户组id查询当前组的用户集合")
    //@PermitAll
    //@PreAuthorize("@ss.hasPermission('system:user:list')")
    public CommonResult<List<UserInfoDTO>> getGroupUserPage(@ApiParam(name = "groupSid", value = "123", required = true) @RequestParam("groupSid") String groupSid) {
        if (StringUtils.isBlank(groupSid)) {
            throw new ServiceException(SystemErrorEnum.PARAM_IS_NULL);
        }
        return CommonResult.success(userService.getGroupUserList(groupSid));
    }










}
