package com.bot.data.collection.system.model.rsp;

import com.bot.data.collection.common.utils.DateUtils;
import com.bot.data.collection.system.model.base.UserGroupBaseVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@ApiModel(value = "用户组信息 Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class UserGroupResp extends UserGroupBaseVO {
    @ApiModelProperty(value = "111", name = "主键字段名称", required = true)
    private String sid;

    @ApiModelProperty(value = "zs", name = "创建人", required = true)
    private String creator;

    @ApiModelProperty(value = "111", name = "用户集合", required = true)
    private List<String> userSidList;

    @ApiModelProperty(value = "2022-07-01 00:00:00", name = "创建时间", required = true)
    @JsonFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "2022-07-01 00:00:00", name = "更新时间", required = true)
    @JsonFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime updateTime;

}
