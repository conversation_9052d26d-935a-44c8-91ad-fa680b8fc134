package com.bot.data.collection.system.mapper;

import com.bot.data.collection.common.mybatis.mapper.BaseMapperX;
import com.bot.data.collection.common.pojo.PageResult;
import com.bot.data.collection.system.model.entity.RoleDO;
import com.bot.data.collection.common.mybatis.query.LambdaQueryWrapperX;
import com.bot.data.collection.system.model.req.RolePageReq;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Set;

@Mapper
public interface RoleMapper extends BaseMapperX<RoleDO> {

    @Select("select `code` from system_role where sid in (select role_sid from system_user_group_role where user_group_sid in (select sid from system_user_group where sid in (select user_group_sid from system_user_user_group where user_sid = #{userId} and deleted = 0) and `status` = 0 and deleted = 0) and deleted = 0) and `status` = 0 and deleted = 0;")
    Set<String> selectRoleCodesByUserId(@Param("userId") String userSid);

    default Long selectCountByName(String sid, String name) {
        return selectCount(new LambdaQueryWrapperX<RoleDO>()
                .eqIfPresent(RoleDO::getName, name)
                .neIfPresent(RoleDO::getSid, sid));
    }

    default Long selectCountByCode(String sid, String code) {
        return selectCount(new LambdaQueryWrapperX<RoleDO>()
                .eqIfPresent(RoleDO::getCode, code)
                .neIfPresent(RoleDO::getSid, sid));
    }
    default PageResult<RoleDO> selectPage(RolePageReq reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<RoleDO>()
                .likeIfPresent(RoleDO::getName, reqVO.getName())
                //.betweenIfPresent(UserGroupDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(RoleDO::getSid));
    }



}
