package com.bot.data.collection.system.convert;

import com.bot.data.collection.system.model.entity.OAuth2AccessTokenDO;
import com.bot.data.collection.system.model.dto.OAuth2AccessTokenCheckRespDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
@Mapper
public interface OAuth2TokenConvert {
    OAuth2TokenConvert INSTANCE = Mappers.getMapper(OAuth2TokenConvert.class);

    OAuth2AccessTokenCheckRespDTO convert(OAuth2AccessTokenDO bean);


}
