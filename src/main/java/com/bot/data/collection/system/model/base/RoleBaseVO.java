package com.bot.data.collection.system.model.base;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 角色 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class RoleBaseVO {

    @ApiModelProperty(value = "管理员", name = "角色名称", required = true)
    @NotBlank(message = "角色名称不能为空")
    @Size(max = 30, message = "角色名称长度不能超过30个字符")
    private String name;

    @ApiModelProperty(value = "admin", name = "角色编码", required = true)
    @Size(max = 100, message = "角色标志长度不能超过100个字符")
    private String code;

    @ApiModelProperty(value = "我是一个角色", name = "备注")
    private String remark;

}
