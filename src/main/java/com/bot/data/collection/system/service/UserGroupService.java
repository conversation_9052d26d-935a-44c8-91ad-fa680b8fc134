package com.bot.data.collection.system.service;

import com.bot.data.collection.common.pojo.PageResult;
import com.bot.data.collection.system.model.dto.UserGroupInfoDTO;
import com.bot.data.collection.system.model.req.UserGroupCreateReq;
import com.bot.data.collection.system.model.req.UserGroupUpdateReq;
import com.bot.data.collection.system.model.rsp.UserGroupResp;
import com.bot.data.collection.system.model.req.UserGroupPageReq;

import java.util.List;

public interface UserGroupService {

    String createUserGroup(UserGroupCreateReq reqVO);

    void updateUserGroup(UserGroupUpdateReq reqVO);

    void deleteUserGroup(String id);

    UserGroupResp getUserGroup(String sid);

    PageResult<UserGroupResp> getUserGroupPage(UserGroupPageReq reqVO);

    /**
     * 查询用户组信息集合
     * @return
     */
    List<UserGroupInfoDTO> getUserGroupInfoList();

    /**
     * 查询角色绑定的用户组sid集合
     * @param roleSid
     * @return
     */
    List<String> getRoleUserGroupSidList(String roleSid);



}
