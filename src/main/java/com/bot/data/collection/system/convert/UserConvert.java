package com.bot.data.collection.system.convert;

import com.bot.data.collection.server.model.dto.UserLockDTO;
import com.bot.data.collection.system.model.dto.UserInfoDTO;
import com.bot.data.collection.system.model.entity.UserDO;
import com.bot.data.collection.common.pojo.PageResult;
import com.bot.data.collection.system.model.req.UserCreateReq;
import com.bot.data.collection.system.model.req.UserUpdateReq;
import com.bot.data.collection.system.model.rsp.UserResp;
import com.bot.data.collection.system.utils.LoginUser;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface UserConvert {
    UserConvert INSTANCE = Mappers.getMapper(UserConvert.class);

    UserDO convert(UserCreateReq bean);

    UserDO convertUserDO(UserUpdateReq bean);

    UserResp convert(UserDO bean);

    List<UserResp> convert(List<UserDO> bean);

    PageResult<UserResp> convertPage(PageResult<UserDO> pageResult);

    List<UserInfoDTO> convertList(List<UserDO> bean);

    UserLockDTO convertUserLockDTO(LoginUser request);
}
