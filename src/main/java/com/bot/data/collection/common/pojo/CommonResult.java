package com.bot.data.collection.common.pojo;

import com.bot.data.collection.common.constants.ErrorEnum;
import com.bot.data.collection.common.utils.DateUtils;
//import com.bot.collection.data.system.constants.SystemErrorEnum;
import com.bot.data.collection.system.constants.SystemErrorEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CommonResult<T> implements Serializable {
    protected static final DateTimeFormatter df = DateTimeFormatter.ofPattern(DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND);
    protected int code;
    protected String msg;
    protected String serverTime;
    protected T data;

    public static <T> CommonResult<T> success() {
        return CommonResult.<T>builder()
                .code(ErrorEnum.SUCCESS.getCode())
                .msg(ErrorEnum.SUCCESS.getMessage())
                .serverTime(df.format(LocalDateTime.now()))
                .build();
    }

    public static <T> CommonResult<T> success(T data) {
        return CommonResult.<T>builder()
                .code(ErrorEnum.SUCCESS.getCode())
                .msg(ErrorEnum.SUCCESS.getMessage())
                .data(data)
                .serverTime(df.format(LocalDateTime.now()))
                .build();
    }

    public static <T> CommonResult<T> error(ErrorEnum errorEnum) {
        return CommonResult.<T>builder()
                .code(errorEnum.getCode())
                .msg(errorEnum.getMessage())
                .serverTime(df.format(LocalDateTime.now()))
                .build();
    }

    public static <T> CommonResult<T> warning(ErrorEnum errorEnum) {
        return CommonResult.<T>builder()
                .code(errorEnum.getCode())
                .msg(errorEnum.getMessage())
                .serverTime(df.format(LocalDateTime.now()))
                .build();
    }

    public static <T> CommonResult<T> error(int code, String msg) {
        return CommonResult.<T>builder()
                .code(code)
                .msg(msg)
                .serverTime(df.format(LocalDateTime.now()))
                .build();
    }

    public static <T> CommonResult<T> error(SystemErrorEnum systemErrorEnum) {
        return CommonResult.<T>builder()
                .code(systemErrorEnum.getCode())
                .msg(systemErrorEnum.getMessage())
                .serverTime(df.format(LocalDateTime.now()))
                .build();
    }

}
