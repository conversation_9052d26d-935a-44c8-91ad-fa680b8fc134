package com.bot.data.collection.common.utils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class ValidatorUtil {

    public static boolean validatePhoneNumber(String phoneNumber) {
        String PHONE_REGEX = "^(?:(?:\\+|00)86)?1[3-9]\\d{9}$";

        // 使用正则表达式匹配手机号格式
        return Pattern.matches(PHONE_REGEX, phoneNumber);
    }

    public static boolean validatePassword(String password) {
        String regex = "^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[@#$%^&+=]).{8,}$";

        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(password);

        return matcher.matches();
    }

    public static void main(String[] args) {
        String password1 = "StrongPass123#";
        String password2 = "Weak123";
        String password3 = "botSmart@666";


        System.out.println("Password1 is valid: " + validatePassword(password1));
        System.out.println("Password2 is valid: " + validatePassword(password2));
        System.out.println("Password2 is valid: " + validatePassword(password3));
    }

}
