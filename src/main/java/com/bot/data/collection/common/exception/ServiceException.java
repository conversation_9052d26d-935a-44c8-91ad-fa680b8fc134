package com.bot.data.collection.common.exception;

import com.bot.data.collection.common.constants.ErrorEnum;
import com.bot.data.collection.server.constants.ServerErrorEnum;
import com.bot.data.collection.system.constants.SystemErrorEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class ServiceException extends RuntimeException{
    /**
     * 业务错误码
     *
     */
    private Integer code;
    /**
     * 错误提示
     */
    private String message;

    public ServiceException() {
    }

    public ServiceException(Integer code, String message) {
        //super(message);
        this.code = code;
        this.message = message;
    }

    public ServiceException(ErrorEnum errorEnum) {
        this.code = errorEnum.getCode();
        this.message = errorEnum.getMessage();
    }

    public ServiceException(SystemErrorEnum errorEnum) {
        this.code = errorEnum.getCode();
        this.message = errorEnum.getMessage();
    }

    public ServiceException(ServerErrorEnum errorEnum) {
        this.code = errorEnum.getCode();
        this.message = errorEnum.getMessage();
    }
}
