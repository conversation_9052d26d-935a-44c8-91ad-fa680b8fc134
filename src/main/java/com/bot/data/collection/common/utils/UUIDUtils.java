package com.bot.data.collection.common.utils;

import com.fasterxml.uuid.Generators;
import com.fasterxml.uuid.impl.TimeBasedGenerator;

import java.time.Instant;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;


public class UUIDUtils {

    private static final int JVM = (int) (System.currentTimeMillis() >>> 8);
    private static short counter = (short) 0;
    static TimeBasedGenerator uuidGenerator = Generators.timeBasedGenerator();
    private static String temp = null;

    private static String format(short shortValue) {
        String formatted = Integer.toHexString(shortValue);
        StringBuilder buf = new StringBuilder("0000");
        buf.replace(4 - formatted.length(), 4, formatted);
        return buf.toString();
    }

    private static String format(int intValue) {
        String formatted = Integer.toHexString(intValue);
        StringBuilder buf = new StringBuilder("00000000");
        buf.replace(8 - formatted.length(), 8, formatted);
        return buf.toString();
    }

    protected static short getCount() {
        synchronized (UUIDUtils.class) {
            if (counter < 0) counter = 0;
            return counter++;
        }
    }

    protected static short getHiTime() {
        return (short) ( System.currentTimeMillis() >>> 32 );
    }

    protected static int getLoTime() {
        return (int) System.currentTimeMillis();
    }
    protected static int getJVM() {
        return JVM;
    }

    public static synchronized String getUUID() {
        String[] group =  uuidGenerator.generate().toString().split("-");
        return String.valueOf(Integer.parseInt(group[2].substring(0, 1)) + 4)
                + group[2].substring(1, 4)
                + format(getJVM())
                + format(getHiTime())
                + format(getLoTime())
                + group[0];
    }

    public static synchronized String getSno(){
        Instant flag = Instant.now().plusNanos(1);
        while (Instant.now().isBefore(flag) || Instant.now().equals(flag)){
            temp = null;
        }
        return String.valueOf(System.currentTimeMillis());
    }


    public static void main(String[] args) throws InterruptedException {


        List<String> uuids = new ArrayList<>();

        for (int i = 0; i < 1000000; i++) {
            Thread t = new Thread(new Runnable() {
                @Override
                public void run() {
                    String uuid = getUUID();
                    uuids.add(uuid);
                    System.out.println("uuid:" + uuid);
                    String sno = getSno();
                    System.out.println("sno:" + sno);
                }
            });
            t.start();
            t.join();
        }

        System.out.println(uuids.size());
        HashSet<String> set = new HashSet<>(uuids);
        System.out.println(set.size());
    }
}