package com.bot.data.collection.common.utils;


import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class SheetData {

    private String sheetName = "";
    private List<String> header = new ArrayList<>();
    private List<List<Object>> datas = new ArrayList<>();

    /*public List<List<String>> toExcelHeader() {
        List<List<String>> headList = new ArrayList<List<String>>();
        for (String row : header) {
            List<String> h = new ArrayList<>();
            h.add(row);
            headList.add(h);
        }
        return headList;
    }*/
}