package com.bot.data.collection.common.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.bot.data.collection.server.constants.EsConstant;
import com.bot.data.collection.server.constants.EsFieldConstant;
import com.bot.data.collection.server.model.es.EsAggregationDTO;
import com.bot.data.collection.server.model.es.EsBucketDTO;
import com.bot.data.collection.server.model.es.EsMappingsDTO;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.DocWriteRequest;
import org.elasticsearch.action.DocWriteResponse;
import org.elasticsearch.action.admin.indices.alias.get.GetAliasesRequest;
import org.elasticsearch.action.admin.indices.create.CreateIndexRequest;
import org.elasticsearch.action.admin.indices.create.CreateIndexResponse;
import org.elasticsearch.action.admin.indices.delete.DeleteIndexRequest;
import org.elasticsearch.action.admin.indices.get.GetIndexRequest;
import org.elasticsearch.action.admin.indices.mapping.get.GetMappingsRequest;
import org.elasticsearch.action.admin.indices.mapping.get.GetMappingsResponse;
import org.elasticsearch.action.admin.indices.settings.get.GetSettingsRequest;
import org.elasticsearch.action.admin.indices.settings.get.GetSettingsResponse;
import org.elasticsearch.action.admin.indices.settings.put.UpdateSettingsRequest;
import org.elasticsearch.action.bulk.BulkItemResponse;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.delete.DeleteResponse;
import org.elasticsearch.action.get.GetRequest;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.support.WriteRequest;
import org.elasticsearch.action.support.master.AcknowledgedResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.*;
import org.elasticsearch.client.core.CountRequest;
import org.elasticsearch.client.core.CountResponse;
import org.elasticsearch.cluster.metadata.AliasMetadata;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.reindex.BulkByScrollResponse;
import org.elasticsearch.index.reindex.UpdateByQueryRequest;
import org.elasticsearch.script.Script;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.elasticsearch.xcontent.XContentType;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.LockSupport;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ElasticsearchTemplate {

    @Resource
    private RestHighLevelClient restHighLevelClient;
    private final Object lock = new Object();
    private static final String TYPE = "_doc";

    /**
     * 查询某个索引的数据
     *
     * @param indexName 索引名称
     * @param clazz     返回的数据类型
     * @return java.util.List<T> 返回列表
     * @date 2019/12/12
     */
    public <T> List<T> findIndex(String indexName, Class<T> clazz) {
        try {
            SearchRequest searchRequest = new SearchRequest(indexName);
            SearchResponse getResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits = getResponse.getHits();
            List<T> results = new ArrayList<>();
            for (SearchHit hit : searchHits) {
                T t = JSON.parseObject(hit.getSourceAsString(), clazz);
                results.add(t);
            }
            return results;
        } catch (IOException e) {
            throw new RuntimeException("findIndex Exception", e);
        }
    }

    /**
     * 模糊匹配索引
     *
     * @param indexName 索引名称
     * @return java.util.List<T> 返回列表
     * @date 2019/12/12
     */
    public List<String> selectIndex(String indexName) {
        try {
            GetAliasesRequest aliasesRequest = new GetAliasesRequest();
            GetAliasesResponse getResponse = restHighLevelClient.indices().getAlias(aliasesRequest, RequestOptions.DEFAULT);
            Map<String, Set<AliasMetadata>> map = getResponse.getAliases();
            List<String> ret = map.entrySet()
                    .stream().filter(x -> x.getKey().contains(indexName))
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toList());
            return ret;
        } catch (IOException e) {
            throw new RuntimeException("findIndex Exception", e);
        }
    }


    /**
     * 根据id获取索引的数据
     *
     * @param indexNames 索引名称
     * @param id         id
     * @param clazz      返回的结果类型class
     * @return T            返回对应结果类型的结果
     * @date 2019/12/12
     */
    public <T> T getById(String indexNames, String id, Class<T> clazz) {
        try {
            GetRequest request = new GetRequest(indexNames, TYPE, id);
            GetResponse getReponse = restHighLevelClient.get(request, RequestOptions.DEFAULT);
            if (getReponse.isExists()) {
                String result = getReponse.getSourceAsString();
                T t = JSONObject.parseObject(result, clazz);
                return t;
            }
        } catch (Exception e) {
            throw new RuntimeException("getById exception", e);
        }
        return null;
    }

    public JSONObject getById(String indexNames, String id) {
        try {
            GetRequest request = new GetRequest(indexNames, TYPE, id);
            GetResponse getReponse = restHighLevelClient.get(request, RequestOptions.DEFAULT);
            if (getReponse.isExists()) {
                return JSONObject.parseObject(getReponse.getSourceAsString());
            }
        } catch (Exception e) {
            throw new RuntimeException("getById exception", e);
        }
        return null;
    }
    public Map<String, Object> getDataMapById(String indexNames, String id) {
        try {
            GetRequest request = new GetRequest(indexNames, TYPE, id);
            GetResponse getReponse = restHighLevelClient.get(request, RequestOptions.DEFAULT);
            if (getReponse.isExists()) {
                return getReponse.getSourceAsMap();
            }
        } catch (Exception e) {
            throw new RuntimeException("getById exception", e);
        }
        return null;
    }
    /**
     * 根据ids批量获取数据，默认根据id排序
     *
     * @param indexNames 索引名称
     * @param clazz      返回类型
     * @param ids        id数组
     * @return java.util.List<T>
     * @date 2019/12/12
     */
    public <T> List<T> getByIds(String indexNames, Class<T> clazz, String[] ids) {
        try {
            SearchRequest searchRequest = new SearchRequest(indexNames);
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            QueryBuilder queryBuilder = QueryBuilders.idsQuery().addIds(ids);
            sourceBuilder.query(queryBuilder);
            sourceBuilder.size(ids.length);
            //id排序
            sourceBuilder.sort(new FieldSortBuilder("_id"));
            searchRequest.source(sourceBuilder);
            searchRequest.types(TYPE);
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits = searchResponse.getHits();
            List<T> results = new ArrayList<>();
            for (SearchHit hit : searchHits) {
                T t = JSON.parseObject(hit.getSourceAsString(), clazz);
                results.add(t);
            }
            return results;
        } catch (Exception e) {
            throw new RuntimeException("multiGetByIds exception", e);
        }
    }

    /**
     * 根据ids分页获取数据
     *
     * @param indexNames 索引名称
     * @param clazz      类型
     * @param ids        id数组
     * @param startIndex 开始index
     * @param pageSize   每页显示多少数据
     * @return java.util.List<T>
     * @date 2019/12/12
     */
    public <T> List<T> getByIds(String indexNames, Class<T> clazz, String[] ids, int startIndex, int pageSize) {
        try {
            SearchRequest searchRequest = new SearchRequest(indexNames);
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            QueryBuilder queryBuilder = QueryBuilders.idsQuery().addIds(ids);
            sourceBuilder.query(queryBuilder);
            sourceBuilder.from(startIndex);
            sourceBuilder.size(pageSize);
            //id排序
            sourceBuilder.sort(new FieldSortBuilder("_id"));
            searchRequest.source(sourceBuilder);
            searchRequest.types(TYPE);
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits = searchResponse.getHits();
            List<T> results = new ArrayList<>();
            for (SearchHit hit : searchHits) {
                T t = JSON.parseObject(hit.getSourceAsString(), clazz);
                results.add(t);
            }
            return results;
        } catch (Exception e) {
            throw new RuntimeException("multiGetByIds exception", e);
        }
    }

    /**
     * 排序分页
     *
     * @param indexNames 索引名称
     * @param clazz      类型
     * @param sortName   排序名称
     * @param order      正倒序
     * @param start      开始位置
     * @param size       每页条数
     * @return java.util.List<T>
     * @date 2019/12/12
     */
    public <T> List<T> findPageBySort(String indexNames, Class<T> clazz, String sortName, SortOrder order, int start, int size) {
        try {
            SearchRequest searchRequest = new SearchRequest(indexNames);
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            sourceBuilder.from(start);
            sourceBuilder.size(size);
            //rank排序
            sourceBuilder.sort(new FieldSortBuilder(sortName).order(order));
            searchRequest.source(sourceBuilder);
            searchRequest.types(TYPE);
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits = searchResponse.getHits();
            List<T> results = new ArrayList<>();
            for (SearchHit hit : searchHits) {
                T t = JSON.parseObject(hit.getSourceAsString(), clazz);
                results.add(t);
            }
            return results;
        } catch (Exception e) {
            throw new RuntimeException("multiGetByIds exception", e);
        }
    }

    /**
     * 根据条件获取索引的文档信息（排序 + 分页）
     * @param indexNames 索引名
     * @param sortName 排序字段
     * @param order 排序方式
     * @param start 起始索引
     * @param size 条数
     * @return
     */
    public JSONArray findPageBySort(String indexNames, String sortName, SortOrder order, int start, int size) {
        try {
            SearchRequest searchRequest = new SearchRequest(indexNames);
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            sourceBuilder.from(start);
            sourceBuilder.size(size);
            //rank排序
            sourceBuilder.sort(new FieldSortBuilder(sortName).order(order));
            searchRequest.source(sourceBuilder);
            searchRequest.types(TYPE);
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits = searchResponse.getHits();
            JSONArray results = new JSONArray();
            for (SearchHit hit : searchHits) {
                results.add(JSON.parse(hit.getSourceAsString()));
            }
            return results;
        } catch (Exception e) {
            throw new RuntimeException("multiGetByIds exception", e);
        }
    }

    /**
     * 根据条件获取索引的文档信息（排序 + 分页）
     * @param indexNames 索引名
     * @param sortName 排序字段
     * @param order 排序方式
     * @param start 起始索引
     * @param size 条数
     * @return
     */
    public JSONArray findPageByQuerySort(String indexNames, QueryBuilder queryBuilder, String sortName, SortOrder order, int start, int size) {
        try {
            SearchRequest searchRequest = new SearchRequest(indexNames);
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            sourceBuilder.query(queryBuilder);
            sourceBuilder.from(start);
            sourceBuilder.size(size);
            //rank排序
            sourceBuilder.sort(new FieldSortBuilder(sortName).order(order));
            searchRequest.source(sourceBuilder);
            searchRequest.types(TYPE);
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits = searchResponse.getHits();
            JSONArray results = new JSONArray();
            for (SearchHit hit : searchHits) {
                results.add(JSON.parse(hit.getSourceAsString()));
            }
            return results;
        } catch (Exception e) {
            throw new RuntimeException("multiGetByIds exception", e);
        }
    }

    /**
     * 根据条件获取索引的文档信息（排序 + 分页）
     * @param indexNames 索引名
     * @param sortName 排序字段
     * @param order 排序方式
     * @param start 起始索引
     * @param size 条数
     * @return
     */
    public List<Map<String, Object>> findPageByQuerySortList(String indexNames, QueryBuilder queryBuilder, String sortName, SortOrder order, int start, int size) {
        try {
            SearchRequest searchRequest = new SearchRequest(indexNames);
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            sourceBuilder.query(queryBuilder);
            sourceBuilder.from(start);
            sourceBuilder.size(size);
            //rank排序
            sourceBuilder.sort(new FieldSortBuilder(sortName).order(order));
            searchRequest.source(sourceBuilder);
            searchRequest.types(TYPE);
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits = searchResponse.getHits();
            List<Map<String, Object>> results = new ArrayList<>();
            for (SearchHit hit : searchHits) {
                Map<String, Object> map = hit.getSourceAsMap();
                map.put(EsFieldConstant.ID, hit.getId());
                results.add(map);
            }
            return results;
        } catch (Exception e) {
            throw new RuntimeException("multiGetByIds exception", e);
        }
    }

    public Set<String> findIdsByQuery(String indexNames, QueryBuilder queryBuilder) {
        try {
            SearchRequest searchRequest = new SearchRequest(indexNames);
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            sourceBuilder.query(queryBuilder);

            //sourceBuilder.fetchSource(new String[]{"your_field_name"}, null); // 指定要返回的字段，这里只返回单个字段
            sourceBuilder.fetchSource(false);
            //sourceBuilder.fetchSource(false, EsFieldConstant.ID);
            searchRequest.source(sourceBuilder);
            searchRequest.types(TYPE);
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits = searchResponse.getHits();
            Set<String> results = new HashSet<>();
            for (SearchHit hit : searchHits) {
                /*Map<String, Object> map = hit.getSourceAsMap();
                map.put(EsFieldConstant.ID, hit.getId());
                results.add(map);*/
                results.add(hit.getId());
            }
            return results;
        } catch (Exception e) {
            throw new RuntimeException("multiGetByIds exception", e);
        }
    }

    public JSONArray findByQuery(String indexName, QueryBuilder queryBuilder, SortBuilder sort, Integer size){
        try {
            SearchRequest searchRequest = new SearchRequest(indexName);
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            if (Objects.isNull(size)){
                sourceBuilder.size(10000);
            }else{
                sourceBuilder.size(size);
            }
            sourceBuilder.query(queryBuilder);
            sourceBuilder.sort(sort);
            searchRequest.source(sourceBuilder);
            searchRequest.types(TYPE);
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits = searchResponse.getHits();
            JSONArray results = new JSONArray();
            for (SearchHit hit : searchHits) {
                results.add(JSON.parse(hit.getSourceAsString()));
            }
            return results;
        } catch (Exception e) {
            throw new RuntimeException("multiGetByIds exception", e);
        }
    }

    /**
     * 根据条件获取索引的文档信息
     * @param indexName 索引名
     * @param queryBuilder 条件
     * @param size 条数
     * @return
     */
    public JSONArray findByQuery(String indexName, QueryBuilder queryBuilder, Integer size) {
        try {
            //SearchRequest对象：搜索请求对象
            SearchRequest searchRequest = new SearchRequest(indexName);
            //SearchSourceBuilder对象：构建搜索请求的查询条件
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            if (Objects.isNull(size)){
                sourceBuilder.size(10000);
            }else{
                sourceBuilder.size(size);
            }
            sourceBuilder.query(queryBuilder);
            searchRequest.source(sourceBuilder);
            searchRequest.types(TYPE);
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            //获取匹配的信息
            SearchHits searchHits = searchResponse.getHits();
            JSONArray results = new JSONArray();
            for (SearchHit hit : searchHits) {
                results.add(JSON.parse(hit.getSourceAsString()));
            }
            return results;
        } catch (Exception e) {
            throw new RuntimeException("multiGetByIds exception", e);
        }
    }


    public <T> List<T> findPageSearch(String indexNames, Integer pageStart, Integer pageSize, List<SortBuilder> sortBuilders, QueryBuilder queryBuilder, Class<T> clazz){
        return findPageSearch(indexNames, pageStart, pageSize, sortBuilders, queryBuilder, clazz, false);
    }
    /**
     * 搜索分页
     *
     * @param indexNames   索引名称
     * @param pageStart    分页开始位置
     * @param pageSize     每页显示条数
     * @param sortBuilders 排序Builder
     * @param queryBuilder 查询Builder
     * @param clazz        返回类型
     * @return java.util.List<T>
     * @date 2019/12/12
     */
    public <T> List<T> findPageSearch(String indexNames, Integer pageStart, Integer pageSize, List<SortBuilder> sortBuilders, QueryBuilder queryBuilder, Class<T> clazz, Boolean idContaint) {
        try {
            SearchRequest searchRequest = new SearchRequest(indexNames);
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            if (pageStart != null) {
                sourceBuilder.from(pageStart);
                sourceBuilder.size(pageSize);
            }
            sourceBuilder.query(queryBuilder);
            if (sortBuilders != null) {
                //rank排序
                for (SortBuilder builder : sortBuilders) {
                    sourceBuilder.sort(builder);
                }
            }
            searchRequest.source(sourceBuilder);
            searchRequest.types(TYPE);
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits = searchResponse.getHits();
            List<T> results = new ArrayList<>();
            for (SearchHit hit : searchHits) {
                T t = JSON.parseObject(hit.getSourceAsString(), clazz);
                if (idContaint && t instanceof JSONObject) {
                    ((JSONObject)t).put("_id", hit.getId());
                }
                results.add(t);
            }
            return results;
        } catch (Exception e) {
            throw new RuntimeException("multiGetByIds exception", e);
        }
    }

    /**
     * 查询该索引的文档总条数
     * @param indexNames
     * @param queryBuilder
     * @return
     */
    public Long findPageTotal(String indexNames, QueryBuilder queryBuilder){
        try {
            //SearchSourceBuilder对象：构建查询条件
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            sourceBuilder.query(queryBuilder);
            //CountRequest对象：设置索引名称、查询条件
            CountRequest countRequest = new CountRequest(indexNames);
            countRequest.source(sourceBuilder);
            CountResponse countResponse;
            long count = 0L;
            try {
                countResponse = restHighLevelClient.count(countRequest, RequestOptions.DEFAULT);
                count = countResponse != null ? countResponse.getCount() : 0;
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (count >= 10000) {
                //GetSettingsRequest对象：获取索引的配置信息
                GetSettingsRequest getSettings = new GetSettingsRequest().indices(indexNames);
                GetSettingsResponse getSettingsResponse = restHighLevelClient.indices().getSettings(getSettings, RequestOptions.DEFAULT);
                String numberOfShardsString = getSettingsResponse.getSetting(indexNames, "index.max_result_window");
                if (Objects.isNull(numberOfShardsString)) {
                    Map<String, Object> settings = new HashMap<>();
                    settings.put("max_result_window", 1000000000);
                    putSetting(indexNames, settings);
                }
            }
            return count;
        } catch (Exception e) {
            throw new RuntimeException("multiGetByIds exception", e);
        }
    }

    /**
     * 判断索引是否存在（可拼接多个）
     * @param esIndex
     * @param retryNum 重试次数
     * @return
     */
    public boolean existsIndex(String esIndex, Integer retryNum) {
        boolean exist = false;
        try {
            synchronized (lock) {
                GetIndexRequest indexRequest = new GetIndexRequest().indices(esIndex);
//                log.error(JSONObject.toJSONString(indexRequest));
//                log.error(esIndex);
                exist = restHighLevelClient.indices().exists(indexRequest, RequestOptions.DEFAULT);
            }
        } catch (IOException e) {
            if (retryNum < 0) {
                LockSupport.parkNanos(TimeUnit.SECONDS.toNanos(1));
                throw new RuntimeException("existsIndex exception", e);
            }else{
                return existsIndex(esIndex, retryNum - 1);
            }
        }
        return exist;
    }
    public boolean existsIndex(String esIndex) {
        return existsIndex(esIndex, 5);
    }

    /**
     * 创建索引
     *
     * @param index      索引名称
     * @param settings   setting设置，可空
     * @param properties properties设置
     * @return boolean
     * @date 2019/12/11
     */
    public boolean createIndex(String index, Map<String, Object> settings, Map<String, Map<String, String>> properties) {
        //创建索引请求对象，并设置索引名称
        CreateIndexRequest createIndexRequest = new CreateIndexRequest(index);
        //设置索引参数
        Settings.Builder builder = Settings.builder();
        if (!CollectionUtils.isEmpty(settings)) {
            for (Map.Entry<String, Object> entry : settings.entrySet()) {
                if (entry.getValue() instanceof String) {
                    builder.put(entry.getKey(), (String) entry.getValue());
                } else if (entry.getValue() instanceof Integer) {
                    builder.put(entry.getKey(), (Integer) entry.getValue());
                }
            }
            createIndexRequest.settings(builder);
        }
        if (!CollectionUtils.isEmpty(properties)) {
            JSONObject sourceJson = new JSONObject();
            sourceJson.put("properties", JSONObject.toJSON(properties));
            //设置映射
            createIndexRequest.mapping(TYPE, sourceJson.toJSONString(), XContentType.JSON);
        }
        //创建索引操作客户端
        IndicesClient indices = restHighLevelClient.indices();
        try {
            //创建响应对象
            CreateIndexResponse createIndexResponse = indices.create(createIndexRequest, RequestOptions.DEFAULT);
            return createIndexResponse.isAcknowledged();
        } catch (IOException e) {
            log.error("创建索引异常", e);
            throw new RuntimeException("创建索引异常", e);
        }
    }

    /**
     * 删除索引
     *
     * @param indexName 索引名称
     * @return boolean
     * @date 2019/12/11
     */
    public boolean deleteIndex(String indexName) {
        try {
            DeleteIndexRequest request = new DeleteIndexRequest(indexName);
            AcknowledgedResponse deleteIndexResponse = restHighLevelClient.indices().delete(request, RequestOptions.DEFAULT);
            return deleteIndexResponse.isAcknowledged();
        } catch (Exception e) {
            log.error("删除索引{}异常", indexName, e);
            throw new RuntimeException("删除索引异常", e);
        }
    }

    /**
     * 删除文档
     *
     * @param indexName
     * @param id
     * @return
     */
    public boolean deleteDoc(String indexName, String id) {
        try {
            DeleteRequest deleteRequest = new DeleteRequest();
            deleteRequest.index(indexName);
            deleteRequest.id(id);
            deleteRequest.setRefreshPolicy(WriteRequest.RefreshPolicy.WAIT_UNTIL);
            DeleteResponse deleteResponse = restHighLevelClient.delete(deleteRequest, RequestOptions.DEFAULT);
            log.error("删除文档{}", id);
            return deleteResponse.forcedRefresh();
        } catch (Exception e) {
            log.error("删除文档{}异常", id, e);
            throw new RuntimeException("删除文档异常", e);
        }
    }

    /**
     * 删除索引
     *
     * @param indexNames 索引名称
     * @return boolean
     * @date 2019/12/11
     */
    public boolean deleteIndex(String... indexNames) {
        try {
            DeleteIndexRequest request = new DeleteIndexRequest(indexNames);
            AcknowledgedResponse deleteIndexResponse = restHighLevelClient.indices().delete(request, RequestOptions.DEFAULT);
            return deleteIndexResponse.isAcknowledged();
        } catch (Exception e) {
            log.error("删除索引{}异常", indexNames, e);
            throw new RuntimeException("删除索引异常", e);
        }
    }

    /**
     * 修改索引的settings
     *
     * @param indexName 索引名称
     * @param settings  索引settings
     * @return boolean
     * @date 2019/12/11
     */
    public boolean putSetting(String indexName, Map<String, Object> settings) {
        UpdateSettingsRequest request = new UpdateSettingsRequest(indexName);
        request.settings(settings);
        try {
            AcknowledgedResponse updateSettingsResponse = restHighLevelClient.indices().putSettings(request, RequestOptions.DEFAULT);
            return updateSettingsResponse.isAcknowledged();
        } catch (IOException e) {
            log.error("索引修改setting异常", e);
            throw new RuntimeException("索引修改setting异常", e);
        }
    }

    /**
     * 插入或更新索引数据
     *
     * @param indexName 索引名称
     * @param id        id的名称
     * @param data      插入的数据
     * @return String       返回插入或者更新的id
     * @date 2019/12/12
     */
    public <T> String insert(String indexName, String id, T data) {
        JSONObject jsonObject = (JSONObject) JSONObject.toJSON(data);
        if (jsonObject.containsKey(id)){
            id = jsonObject.getString(id);
        }
        IndexRequest indexRequest = new IndexRequest(indexName, TYPE, id)
                .source(jsonObject.toJSONString(), XContentType.JSON);
        IndexResponse indexResponse = null;
        try {
            indexResponse = restHighLevelClient.index(indexRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error("es插入异常", e);
            throw new RuntimeException("es插入异常", e);
        }
        if (indexResponse.getResult() == DocWriteResponse.Result.CREATED) {
            return indexResponse.getId();
        } else if (indexResponse.getResult() == DocWriteResponse.Result.UPDATED) {
            return indexResponse.getId();
        }
        return null;
    }

    public <T> void update(String indexName, String id, T data) {
        UpdateRequest request = new UpdateRequest(indexName, TYPE, id);

        request.doc((JSONObject) JSONObject.toJSON(data), XContentType.JSON);;

        try {
            restHighLevelClient.update(request, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error("es更新异常", e);
            throw new RuntimeException("es更新异常", e);
        }
    }

    public boolean updateByQuery(String indexName, BoolQueryBuilder boolQueryBuilder, Script script) {

        UpdateByQueryRequest updateByQuery  = new UpdateByQueryRequest(indexName);
        //设置分片并行
        updateByQuery.setSlices(2);
        //设置版本冲突时继续执行
        updateByQuery.setConflicts("proceed");
        //设置更新完成后刷新索引 ps很重要如果不加可能数据不会实时刷新
        updateByQuery.setRefresh(true);
        //查询条件如果是and关系使用must 如何是or关系使用should

        updateByQuery.setQuery(boolQueryBuilder);
        //设置要修改的内容可以多个值多个用；隔开
        //updateByQuery.setScript(new Script("ctx._source['"+ EsFieldConstant.FIRST_AUDITOR +"']='QQ'; ctx._source.two_auditor = '洛洛';"));
        updateByQuery.setScript(script);
        try {
            BulkByScrollResponse response = restHighLevelClient.updateByQuery(updateByQuery,RequestOptions.DEFAULT);
            return response.getStatus().getUpdated() > 0 ? true:false;
        } catch (IOException e) {
            log.error("es更新异常", e);
            throw new RuntimeException("es更新异常", e);
        }
    }
    public <T> String inserts(String indexName, String id, String data) {
        IndexRequest indexRequest = new IndexRequest(indexName, TYPE, id)
                .source(data, XContentType.JSON);
        IndexResponse indexResponse = null;
        try {
            indexResponse = restHighLevelClient.index(indexRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error("es插入异常", e);
            throw new RuntimeException("es插入异常", e);
        }
        if (indexResponse.getResult() == DocWriteResponse.Result.CREATED) {
            return indexResponse.getId();
        } else if (indexResponse.getResult() == DocWriteResponse.Result.UPDATED) {
            return indexResponse.getId();
        }
        return null;
    }

    /**
     * 批量插入索引数据
     *
     * @param indexName 索引名称
     * @param id        索引的id名称
     * @param list      插入的数据
     * @return boolean
     * @date 2019/12/11
     */
    public <T> boolean batchInsert(String indexName, String id, List<T> list) {
        BulkRequest request = new BulkRequest();
        //空数据直接返回成功
        if (CollectionUtils.isEmpty(list)) {
            return true;
        }
        for (T object : list) {
            JSONObject jsonObject = (JSONObject) JSONObject.toJSON(object);
            String json = jsonObject.toString(SerializerFeature.WriteNullNumberAsZero);
            request.add(new IndexRequest(indexName, TYPE, jsonObject.getString(id))
                    .source(json, XContentType.JSON));
        }
        BulkResponse bulkResponse = null;
        try {
            bulkResponse = restHighLevelClient.bulk(request, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error("批量插入es异常", e);
            throw new RuntimeException("批量插入es异常", e);
        }
        //如果返回结果有一项是失败
        if (bulkResponse != null && bulkResponse.hasFailures()) {
            for (BulkItemResponse bulkItemResponse : bulkResponse) {
                //因为是添加操作，所以只处理添加的响应
                if (bulkItemResponse.getOpType() == DocWriteRequest.OpType.INDEX
                        || bulkItemResponse.getOpType() == DocWriteRequest.OpType.CREATE) {
                    if (bulkItemResponse.isFailed()) {
                        BulkItemResponse.Failure failure = bulkItemResponse.getFailure();
                        log.error("批量插入索引失败:{}", failure);
                    }
                }
            }
            return false;
        } else if (bulkResponse == null) {
            return false;
        }
        return true;
    }

    /**
     * 获取es Mappings结构
     * @param indexName
     * @return
     */
    public EsMappingsDTO getMapping(String indexName) {
        try {
            // 创建GetMappingsRequest
            GetMappingsRequest request = new GetMappingsRequest().indices(indexName);
            // 执行请求，获取映射结构
            GetMappingsResponse response = restHighLevelClient.indices().getMapping(request, RequestOptions.DEFAULT);

            String mapping = response.getMappings().get(indexName).get(TYPE).source().string();

            return JSON.parseObject(mapping, EsMappingsDTO.class);
        } catch (IOException e) {
            throw new RuntimeException("findIndex Exception", e);
        }
    }

    public List<EsBucketDTO> findAggrByQuery(String indexNames, QueryBuilder queryBuilder, AggregationBuilder aggregationBuilder) {
        try {

            SearchRequest searchRequest = new SearchRequest(indexNames);
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            sourceBuilder.query(queryBuilder);
            sourceBuilder.aggregation(aggregationBuilder);
            searchRequest.source(sourceBuilder);
            //searchRequest.types(TYPE);
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            Aggregations aggregations = searchResponse.getAggregations();
            for (Aggregation aggregation : aggregations) {
                if (EsConstant.COLLECT_DATA_DAY_AGG.equals(aggregation.getName())) {
                    EsAggregationDTO dto = JSON.parseObject(JsonUtils.toJsonString(aggregation), EsAggregationDTO.class);
                    if (dto != null) {
                        return dto.getBuckets();
                    }
                }
            }
            return null;
        } catch (Exception e) {
            throw new RuntimeException("multiGetByIds exception", e);
        }
    }
}


