package com.bot.data.collection.common.exception;

import com.bot.data.collection.common.pojo.CommonResult;
import com.bot.data.collection.common.constants.ErrorEnum;

import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindingResult;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

@Slf4j
@ControllerAdvice
public class GlobalExceptionHandler {
    @ResponseBody
    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    public CommonResult<?> handleValidException(MethodArgumentNotValidException e) {
        BindingResult bindingResult = e.getBindingResult();
        StringBuilder message = new StringBuilder();
        if (bindingResult.hasErrors()) {
            List<ObjectError> allErrors = bindingResult.getAllErrors();
            for (ObjectError allError : allErrors) {
                message.append(allError.getDefaultMessage()).append("，");
            }
            message.deleteCharAt(message.length() - 1);
            log.error("调用方法：{}，参数异常：{}", e.getParameter().getMethod().getName(), message);
        }
        return CommonResult.error(ErrorEnum.PARAM_IS_NULL.getCode(), message.toString());
    }

    @ResponseBody
    @ExceptionHandler(value = ServiceException.class)
    public CommonResult<?> handleValidException(ServiceException e) {
        log.error("业务异常：" + e);
        return CommonResult.error(e.getCode(), e.getMessage());
    }
}
