package com.bot.data.collection.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.crypto.Cipher;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESedeKeySpec;
import java.security.Key;
@Slf4j
@Component
public class Des3Utils {

    private final static String encoding = "utf-8";
    private static final String KEY_ALGORITHM = "DESede";
    private static final String DEFAULT_CIPHER_ALGORITHM = "DESede/ECB/PKCS5Padding";// 默认的加密算法

    //private static final String KEY = "xQu5pQEhHXjW5kRDHkltYoaNuIE5Zb2w";
    private static final String KEY = "WWW5pQEhHXjW5kRDHkltYoaNuIE5Zcom";

    public static void main(String[] args){
        String str = "123456";
        String encrypt3DES = encode(str, KEY);
        System.out.println("加密-----------> " + encrypt3DES);
        String decrypt3DES = decode(encrypt3DES, KEY);
        System.out.println("解密-----------> " + decrypt3DES);
    }
    /**
     *
     * 3DES加密 32位密钥
     *
     * @param plainText
     *            普通文本
     * @return
     *
     * @throws Exception
     */

    public static String encode(String plainText) {
        return encode(plainText, KEY);
    }

    public static String encode(String plainText, String secretKey) {
        try {
            Key deskey = null;
            DESedeKeySpec spec = new DESedeKeySpec(secretKey.getBytes());
            SecretKeyFactory keyfactory = SecretKeyFactory
                    .getInstance(KEY_ALGORITHM);
            deskey = keyfactory.generateSecret(spec);
            Cipher cipher = Cipher.getInstance(DEFAULT_CIPHER_ALGORITHM);
            // IvParameterSpec ips = new IvParameterSpec(iv.getBytes());
            cipher.init(Cipher.ENCRYPT_MODE, deskey);
            byte[] encryptData = cipher.doFinal(plainText.getBytes(encoding));
            return bytesToHexString(encryptData);
        }catch (Exception e){
            log.error(e.getMessage());
            return null;
        }
    }



    public static String decode(String plainText){
        return decode(plainText, KEY);
    }
    /**
     *
     * 3DES解密 32位密钥
     *
     * @param encryptText
     *            加密文本
     * @return
     * @throws Exception
     */
    public static String decode(String encryptText, String secretKey) {
        try {
            Key deskey = null;
            DESedeKeySpec spec = new DESedeKeySpec(secretKey.getBytes());
            SecretKeyFactory keyfactory = SecretKeyFactory
                    .getInstance(KEY_ALGORITHM);
            deskey = keyfactory.generateSecret(spec);
            Cipher cipher = Cipher.getInstance(DEFAULT_CIPHER_ALGORITHM);
            // IvParameterSpec ips = new IvParameterSpec(iv.getBytes());
            cipher.init(Cipher.DECRYPT_MODE, deskey);
            byte[] decryptData = cipher.doFinal(hexStringToBytes(encryptText));
            return new String(decryptData, encoding);
        }catch (Exception e){
            log.error(e.getMessage());
            return null;
        }

    }

    public static String bytesToHexString(byte[] bytes) {
        StringBuilder stringBuilder = new StringBuilder();
        if (bytes != null && bytes.length > 0) {
            byte[] arg1 = bytes;
            int arg2 = bytes.length;
            for (int arg3 = 0; arg3 < arg2; ++arg3) {
                byte b = arg1[arg3];
                int v = b & 255;
                String hv = Integer.toHexString(v);
                if (hv.length() < 2) {
                    stringBuilder.append(0);
                }
                stringBuilder.append(hv);
            }
            return stringBuilder.toString().toUpperCase();
        } else {
            return "";
        }
    }

    public static byte[] hexStringToBytes(String hexString) {
        if (hexString != null && !"".equals(hexString)) {
            hexString = hexString.toUpperCase();
            int length = hexString.length() / 2;
            char[] hexChars = hexString.toCharArray();
            byte[] d = new byte[length];
            for (int i = 0; i < length; ++i) {
                int pos = i * 2;
                d[i] = (byte) (charToByte(hexChars[pos]) << 4 | charToByte(hexChars[pos + 1]));
            }
            return d;
        } else {
            return null;
        }
    }

    private static byte charToByte(char c) {
        return (byte) "0123456789ABCDEF".indexOf(c);
    }
}
