package com.bot.data.collection.common.config;


import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.nio.client.HttpAsyncClientBuilder;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;


@Configuration
public class ElasticsearchConfiguration {
    @Value("${elasticsearch.host}")
    private String host;

    @Value("${elasticsearch.port}")
    private String port;

    @Value("${elasticsearch.connTimeout}")
    private int connTimeout;

    @Value("${elasticsearch.socketTimeout}")
    private int socketTimeout;

    @Value("${elasticsearch.connectionRequestTimeout}")
    private int connectionRequestTimeout;

    @Value("${elasticsearch.username}")
    private String USERNAME;

    @Value("${elasticsearch.password}")
    private String PASSWORD;
    @Value("${elasticsearch.max-conn-total}")
    private Integer maxConnTotal;
    @Value("${elasticsearch.max-conn-per-route}")
    private Integer maxConnPerRoute;


    @Bean(destroyMethod = "close", name = "client")
    public RestHighLevelClient initRestClient() {

        //如果没配置密码就可以不用下面这两部
        final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
        credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(USERNAME, PASSWORD));
        String[] strings=host.split(",");
        String[] split=port.split(",");
        List<HttpHost> list=new ArrayList<>();
        for (int i = 0; i < strings.length; i++) {
            list.add(new HttpHost(strings[i], Integer.parseInt(split[i]))) ;
        }
        HttpHost[] httpHosts=list.stream().toArray(HttpHost[]::new);
        RestClientBuilder builder = RestClient.builder(httpHosts)
                .setRequestConfigCallback( new RestClientBuilder.RequestConfigCallback(){
                                               @Override
                                               public RequestConfig.Builder customizeRequestConfig(RequestConfig.Builder requestConfigBuilder) {
                                                   requestConfigBuilder.setConnectTimeout(connTimeout);
                                                   requestConfigBuilder.setSocketTimeout(socketTimeout);
                                                   requestConfigBuilder.setConnectionRequestTimeout(connectionRequestTimeout);
                                                   return requestConfigBuilder;
                                               }
                                           }
                )
                //没有密码可以不用这一个set
                .setHttpClientConfigCallback(new RestClientBuilder.HttpClientConfigCallback() {
                    @Override
                    public HttpAsyncClientBuilder customizeHttpClient(HttpAsyncClientBuilder httpClientBuilder) {
                        httpClientBuilder.disableAuthCaching();
                        httpClientBuilder.setMaxConnTotal(maxConnTotal);
                        httpClientBuilder.setMaxConnPerRoute(maxConnPerRoute);
                        return httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider);
//                return httpClientBuilder;
                    }
                });

        return new RestHighLevelClient(builder);
    }

}


