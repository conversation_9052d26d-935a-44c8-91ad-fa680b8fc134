package com.bot.data.collection.common.utils;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.metadata.data.ReadCellData;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Data
public class ArrayDataListener extends AnalysisEventListener<Map<Integer,String>> {
    private SheetData data = new SheetData();
    @Override
    public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
        super.invokeHead(headMap, context);
        //标题处理
        data.setSheetName(context.readSheetHolder().getSheetName());
        for (Integer key : headMap.keySet()) {
            ReadCellData cellData = headMap.get(key);
            String value = cellData.getStringValue();
            data.getHeader().add(value);
        }
    }

    @Override
    public void invoke(Map<Integer, String> dataMap, AnalysisContext analysisContext) {
        //数据处理
        int lineNum = data.getHeader().size();
        List<Object> line = new ArrayList<>();
        for (int i = 0; i < lineNum; i++) {
            String value = null;
            if (dataMap.containsKey(i)) {
                value = dataMap.get(i);
            }
            line.add(value);
        }
        data.getDatas().add(line);
    }
    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
    }
}
