package com.bot.data.collection.common.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ErrorEnum {

    SUCCESS(200, "SUCCESS"),
    ERROR(500, "业务执行异常"),

    UNAUTHORIZED(401, "账号未登录"),
    //ErrorCode UNAUTHORIZED = new ErrorCode(401, "账号未登录");

    // 用户信息错误
    /*USER_NAME_PASS_ERROR(403001, "用户名密码错误"),
    USER_STATE_ERROR(403002, "用户已禁用，请与系统管理员联系"),*/

    // 系统级错误
    MISSING_PARAM_ERROR(400000, "参数缺失"),
    PARAM_VALUE_ERROR(400001, "参数值不匹配"),
    PARAM_IS_NULL(400002, "参数值为空"),
    UN_LOGGED(400100, "未登录"),
    UN_AUTHENTICATED(400200, "无权限"),

    // 数据解析错误
    GET_DATA_ERROR(500100, "数据解析错误"),
    DATA_BLANK_ERROR(500101, "必填字段缺失"),
    SIGNATURE_ERROR(500102, "签名命错误"),
    DATA_FORMAT_ERROR(500103, "数据格式错误"),
    REQUEST_TIME_EXCEEDED(500104, "请求时间超限"),


    // 其他
    CONTENT_REPEAT(500200, "内容重复"),
    NO_MORE_CONTENT(500201, "没有更多数据"),
    CONTENT_ERROR(500202, "内容错误"),
    CONTENT_NOT_EXIST(500203, "内容不存在"),
    WORD_REPORT_ERROR(500204, "报告生成失败"),
    REPEAT_QUEST_ERROR(500205, "重复请求"),
    TIME_OUT_ERROR(500206, "请求超时"),
    SID_NOT_NULL(500207, "sid不能为空"),
    DATA_NOT_EXISTS(500208, "数据不存在"),


    // 登录错误
    /*CODE_FAILURE(500001, "验证码已过期"),
    CODE_ERROR(500002, "验证码不正确"),
    AUTH_ERROR(500003, "用户名或密码错误"),
    VERIFY_CODE_EXIST_ERROR(500004, "验证码已发送，请勿重复操作"),
    VERIFY_CODE_SEND_ERROR(500005, "验证码发送失败，请验证手机号"),*/

    // 任务队列错误
    TASK_MQ_CONNECT_ERROR(500401, "中台控制端访问错误"),
    TASK_MQ_INIT_ERROR(500402, "任务初始化失败"),


    // ES错误
    ES_CREATE_INDEX_ERROR(500501, "索引创建错误"),
    ES_ADD_ERROR(500502, "数据插入错误"),
    ES_INDEX_NOT_EXISTS(500503, "数据索引不存在"),

    // 数据库错误
    DB_GET_DATA_ERROR(500601, "数据读取错误"),

    // 返回结果
    SEARCH_DATA_NONE(500701, "查询结果返回空"),

    //锁
    LOCK_FAIL(500801, "上锁失败"),
    UNLOCK_FAIL(500802, "解锁失败"),
    LOCK_FAIL_REPEAT(500803, "不能重复上锁"),
    UNLOCK_FAIL_INEXISTENCE(500804, "锁不存在"),



    ;

    private final int code;
    private final String message;
}