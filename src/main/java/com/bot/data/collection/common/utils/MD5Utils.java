package com.bot.data.collection.common.utils;

import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

public class MD5Utils {
    /**
     * 使用md5的算法进行加密
     */
    public static String md5(String plainText) {
        byte[] secretBytes = null;
        try {
            secretBytes = MessageDigest.getInstance("md5").digest(
                    plainText.getBytes());
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("没有md5这个算法！");
        }
        String md5code = new BigInteger(1, secretBytes).toString(16);// 16进制数字
        // 如果生成数字未满32位，需要前面补0
        for (int i = 0; i < 32 - md5code.length(); i++) {
            md5code = "0" + md5code;
        }
        return md5code;
    }

    /**
     * 可逆的的加密解密方法；两次是解密，一次是加密
     * @param inStr
     * @return
     */
    public static String convertMD5(String inStr){

        char[] a = inStr.toCharArray();
        for (int i = 0; i < a.length; i++){
            a[i] = (char) (a[i] ^ 't');
        }
        String s = new String(a);
        return s;

    }

    public static String getMd5(String inStr) {
        try {
            // 生成一个MD5加密计算摘要
            MessageDigest md = MessageDigest.getInstance("MD5");
            // 计算md5函数
            md.update(inStr.getBytes());
            // digest()最后确定返回md5 hash值，返回值为8为字符串。因为md5 hash值是16位的hex值，实际上就是8位的字符
            // BigInteger函数则将8位的字符串转换成16位hex值，用字符串来表示；得到字符串形式的hash值
            String md5 = new BigInteger(1, md.digest()).toString(16);
            //BigInteger会把0省略掉，需补全至32位
            return fillMD5(md5);
        } catch (Exception e) {
            throw new RuntimeException("MD5加密错误:" + e.getMessage(), e);
        }
    }
    public static String fillMD5(String md5)
    {
        return md5.length()==32?md5:fillMD5("0"+md5);
    }


    public static void main(String[] args) {
//        String codePath = "gad";
//        codePath = codePath.replaceAll("(.{3}).","$1_");
////        String ps = "bot@300229" + "ASdfelRtVjBhVzV6YVdkb2RBPT0=" + System.currentTimeMillis();
////        String base = Base64.encodeBase64String(Base64.encodeBase64String(ps.getBytes()).getBytes());
        String s = md5("/api/collection/qb/update2023-04-24 18:41:114028b88187b156430187b1566ddf33d0");
        System.out.println("MD5后："+s);
        System.out.println("MD5后再加密："+convertMD5(s));
        System.out.println("MD5加密后解密："+convertMD5(convertMD5(s)));
        String s2 = convertMD5("1234");
        System.out.println("可逆的加密解密方法之加密："+s2);
        System.out.println("可逆的加密解密方法之解密："+convertMD5(s2));
    }

}
