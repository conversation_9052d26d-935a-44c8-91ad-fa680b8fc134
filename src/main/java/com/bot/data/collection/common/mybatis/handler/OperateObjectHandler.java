package com.bot.data.collection.common.mybatis.handler;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.bot.data.collection.system.utils.LoginUser;
import com.bot.data.collection.system.utils.SecurityFrameworkUtils;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 对象变化的处理器
 * 操作对象时，增加业务逻辑
 */
@Component
public class OperateObjectHandler implements MetaObjectHandler {
    @Override
    public void insertFill(MetaObject metaObject) {
        this.setFieldValByName("createTime", LocalDateTime.now(), metaObject);
        this.setFieldValByName("updateTime", LocalDateTime.now(), metaObject);

        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        if (loginUser != null) {
            String loginUserId = loginUser.getSid();
            this.setFieldValByName("creator", loginUserId, metaObject);
            this.setFieldValByName("updater", loginUserId, metaObject);
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        this.setFieldValByName("updateTime", LocalDateTime.now(), metaObject);
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        if (loginUser != null) {
            String loginUserId = loginUser.getSid();
            this.setFieldValByName("updater", loginUserId, metaObject);
        }
    }
}
